import { createMockStore } from 'redux-logic-test';
import { createBookingLogic } from './createBookingLogic';
import { createBooking as createBooking<PERSON><PERSON><PERSON><PERSON> } from 'lib/clients/createBooking';
import { createBooking, setBooking, trackBooking } from 'store/booking/bookingActions';
import { getFormData, getBookingClientRequestId, getBookingChannel } from 'store/checkout/checkoutSelectors';
import { getQuote, getQuoteReference } from 'store/quote/quoteSelectors';
import { getQuoteDepositPay, getQuoteDepositPayReference } from 'store/quoteDepositPay/quoteDepositPaySelectors';
import { getAccessToken, getQhUserId } from 'store/user/userSelectors';
import { getDeviceFingerprint, getDeviceFingerprintError, getIpAddress, getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import { captureErrorInSentry } from 'lib/errors';
import createBookingPayload from './createBookingPayload';
import { setBookingClientRequestId } from 'store/checkout/checkoutActions';
import { push } from 'connected-next-router';
import { BOOKING_STATES, BOOKING_ERROR_CODES } from 'lib/enums/booking';
import createBookingClientRequestId from 'lib/checkout/createBookingClientRequestId';
import { getRoomAvailabilityMessageForOffer } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';

jest.mock('store/userEnvironment/userEnvironmentSelectors');
jest.mock('store/booking/bookingSelectors');
jest.mock('store/quoteDepositPay/quoteDepositPaySelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('lib/clients/createBooking');
jest.mock('store/user/userSelectors');
jest.mock('lib/errors/captureErrorInSentry');
jest.mock('./createBookingPayload');
jest.mock('lib/checkout/createBookingClientRequestId');
jest.mock('lib/qffAuth');
jest.mock('store/booking/bookingActions', () => ({
  ...jest.requireActual('store/booking/bookingActions'),
  trackBooking: jest.fn(),
}));
jest.mock('components/RoomsAvailabilityMessage/getRoomAvailabilityMessage');

const initialState = {
  booking: {},
  router: {
    location: {
      href: '',
    },
  },
};

let store;
const clientRequestId = 'clientRequestId';
const newClientRequestId = 'newClientRequestId';
const browser = 'browser';
const formData = { paymentMode: 'FULL' };
const originalQuote = 'originalQuote';
const depositPayQuote = 'depositPayQuote';
const originalQuoteReference = 'originalQuoteReference';
const depositPayQuoteReference = 'depositPayQuoteReference';
const deviceFingerprint = 'deviceFingerprint';
const deviceFingerprintError = 'deviceFingerprintError';
const ipAddress = 'ipAddress';
const accessToken = 'accessToken';
const createBookingResponse = { id: 1, state: BOOKING_STATES.BOOKED };
const bookingPayload = { payments: [{ type: 'qff_account' }] };
const qhUserId = 'qhUserId';
const bookingChannel = 'WEB';
const availableRoomsMessage = 'We only have 3 rooms left';

const defaultAction = {
  showMessage: true,
  max_rooms_cutoff: 5,
};

describe('createBookingLogic', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    getBrowser.mockReturnValue(browser);
    getFormData.mockReturnValue(formData);
    getQuote.mockReturnValue(originalQuote);
    getQuoteDepositPay.mockReturnValue(depositPayQuote);
    getQuoteReference.mockReturnValue(originalQuoteReference);
    getQuoteDepositPayReference.mockReturnValue(depositPayQuoteReference);
    getDeviceFingerprint.mockReturnValue(deviceFingerprint);
    getDeviceFingerprintError.mockReturnValue(deviceFingerprintError);
    getIpAddress.mockReturnValue(ipAddress);
    getAccessToken.mockReturnValue(accessToken);
    getQhUserId.mockReturnValue(qhUserId);
    getBookingClientRequestId.mockReturnValue(clientRequestId);
    getBookingChannel.mockReturnValue(bookingChannel);
    getRoomAvailabilityMessageForOffer.mockReturnValue(availableRoomsMessage);
    createBookingPayload.mockReturnValue(bookingPayload);
    createBookingClientRequestId.mockReturnValue(newClientRequestId);
    store = createMockStore({ initialState, logic: [createBookingLogic] });
  });

  afterEach(() => {
    createBookingFromApi.mockReset();
    trackBooking.mockReset();
  });

  describe('with a successful createBooking response', () => {
    beforeEach(() => {
      createBookingFromApi.mockImplementation(() => Promise.resolve(createBookingResponse));
      store.dispatch(createBooking(defaultAction));
    });

    describe('when it is not a deposit pay booking', () => {
      it('creates the booking payload with the original quote', async () => {
        expect(createBookingPayload).toHaveBeenCalledWith({
          bookingChannel,
          formData,
          quoteReference: originalQuoteReference,
          ipAddress,
          deviceFingerprint,
          deviceFingerprintError,
          clientRequestId,
        });
      });
    });

    it('tracks the booking with the correct parameters when available rooms message is shown', async () => {
      getQuote.mockReturnValue({ offer: { allocationsAvailable: 3 } });
      store.dispatch(createBooking(defaultAction));
      await store.whenComplete();

      expect(trackBooking).toHaveBeenCalledWith({
        isRebooked: false,
        booking: createBookingResponse,
        quote: { offer: { allocationsAvailable: 3 } },
        ctaMessage: availableRoomsMessage,
        ctaMessageCategory: 'available rooms',
      });
    });

    it('tracks the booking with the correct parameters when available rooms message is not shown', async () => {
      getQuote.mockReturnValue({ offer: { allocationsAvailable: 0 } });
      createBookingFromApi.mockImplementation(() => Promise.resolve(createBookingResponse));
      store.dispatch(createBooking(defaultAction));
      await store.whenComplete();

      expect(trackBooking).toHaveBeenCalledWith({
        isRebooked: false,
        booking: createBookingResponse,
        quote: { offer: { allocationsAvailable: 0 } },
        ctaMessage: '',
        ctaMessageCategory: '',
      });
    });

    it('tracks the booking with the original quote when isPriceStrikethrough is true', async () => {
      const quoteWithStrikethrough = { offer: { allocationsAvailable: 3, charges: { strikethrough: 100 } } };
      getQuote.mockReturnValue(quoteWithStrikethrough);
      store.dispatch(createBooking({ ...defaultAction, isPriceStrikethrough: true }));
      await store.whenComplete();

      expect(trackBooking).toHaveBeenCalledWith({
        isRebooked: false,
        booking: createBookingResponse,
        quote: quoteWithStrikethrough,
        ctaMessage: availableRoomsMessage,
        ctaMessageCategory: 'available rooms',
      });
    });

    it('tracks the booking with the strikethrough removed from the quote when isPriceStrikethrough is false and strikethrough exists', async () => {
      const quoteWithStrikethrough = { offer: { allocationsAvailable: 3, charges: { strikethrough: 100 } } };
      const quoteWithoutStrikethrough = { offer: { allocationsAvailable: 3, charges: {} } };
      getQuote.mockReturnValue(quoteWithStrikethrough);
      store.dispatch(createBooking({ ...defaultAction, isPriceStrikethrough: false }));
      await store.whenComplete();

      expect(trackBooking).toHaveBeenCalledWith({
        isRebooked: false,
        booking: createBookingResponse,
        quote: quoteWithoutStrikethrough,
        ctaMessage: availableRoomsMessage,
        ctaMessageCategory: 'available rooms',
      });
    });

    describe('when it is a deposit pay booking', () => {
      it('creates the booking payload with the depositPay quote', async () => {
        getFormData.mockReturnValue({ paymentMode: 'DEPOSIT' });
        store.dispatch(createBooking(defaultAction));
        await store.whenComplete();

        expect(createBookingPayload).toHaveBeenCalledWith({
          bookingChannel,
          formData: { paymentMode: 'DEPOSIT' },
          quoteReference: depositPayQuoteReference,
          ipAddress,
          deviceFingerprint,
          deviceFingerprintError,
          clientRequestId,
        });
      });
    });

    it('creates the booking via the api', async () => {
      await store.whenComplete();
      expect(createBookingFromApi).toHaveBeenCalledWith({ payload: bookingPayload, accessToken, qhUserId });
    });

    it('dispatches the setBooking action with the submitting state', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setBooking({ state: BOOKING_STATES.SUBMITTING })]));
    });

    it('dispatches the setBooking action with the booking response', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setBooking(createBookingResponse)]));
    });

    it('dispatches the push with the checkout url', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([push({ pathname: `/bookings/${createBookingResponse.id}` })]));
    });

    it('dispatches the setBookingClientRequestId with a new clientRequestId', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setBookingClientRequestId(newClientRequestId)]));
    });
  });

  describe('with an unsuccessful createBooking response', () => {
    const error = new Error();
    const errors = [{ code: 'validation_failure' }];
    error.response = { status: 500, data: { booking: { errors } } };

    beforeEach(() => {
      createBookingFromApi.mockImplementation(() => Promise.reject(error));
      store.dispatch(createBooking(defaultAction));
    });

    it('dispatches the setBooking action with a submitting booking state', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setBooking({ state: BOOKING_STATES.SUBMITTING })]));
    });

    it('does not log the error with sentry', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(captureErrorInSentry).not.toHaveBeenCalled();
    });

    it('dispatches the setBooking action with a failure state', async () => {
      await store.whenComplete();
      expect(store.actions).toEqual(
        expect.arrayContaining([
          setBooking({
            state: BOOKING_STATES.FAILED,
            errors,
          }),
        ]),
      );
    });
  });

  describe('with a general error', () => {
    const error = new Error();

    beforeEach(() => {
      createBookingPayload.mockImplementation(() => {
        throw error;
      });
      store.dispatch(createBooking(defaultAction));
    });

    it('logs the error with sentry', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(captureErrorInSentry).toHaveBeenCalledWith(error);
    });

    it('dispatches the setBooking action with a failure state', async () => {
      await store.whenComplete();
      expect(store.actions).toEqual(
        expect.arrayContaining([
          setBooking({
            state: BOOKING_STATES.FAILED,
            errors: [
              {
                code: BOOKING_ERROR_CODES.GENERAL_ERROR,
              },
            ],
          }),
        ]),
      );
    });
  });
});
