import { Decimal } from 'decimal.js';
import createBookingPayload from './createBookingPayload';
import { ADYEN_MERCHANT_ID, ADYEN_API_VERSION, HOTELS_BRAND_BOOKING_CHANNEL, BRAND_CODE } from 'config';
import set from 'lodash/set';

const adyenCardData = {
  tokenizedCreditCard: 'tokenizedCreditCard',
  encryptedCardNumber: 'encryptedCardNumber',
  encryptedExpiryMonth: 'encryptedExpiryMonth',
  encryptedExpiryYear: 'encryptedExpiryYear',
  encryptedSecurityCode: 'encryptedSecurityCode',
  holderName: 'holderName',
  cardType: 'cardType',
};

const formData = {
  title: 'Mr',
  firstName: 'Happy',
  lastName: 'Booker',
  emailAddress: '<EMAIL>',
  phoneNumber: '04132234',
  subscribe: false,
  specialRequests: 'specialRequests',
  qffNumber: '12345',
  abn: '54321',
  payments: {
    voucher: {
      amount: new Decimal(20),
      code: 'voucher-code',
    },
    travelPass: {
      amount: new Decimal(20),
      encryptedCardNumber: 'encryptedCardNumber',
      encryptedExpiryMonth: 'encryptedExpiryMonth',
      encryptedExpiryYear: 'encryptedExpiryYear',
    },
    points: {
      amount: new Decimal(5000),
      amountInCash: new Decimal(25.25),
      tierVersionCode: 'tierVersionCode',
      qffNumber: 'qffNumber',
    },
    cash: {
      payableNow: {
        amount: new Decimal(100),
      },
      payableLater: {
        amount: new Decimal(200),
        dueDate: new Date(),
      },
    },
  },
};
const clientRequestId = 'clientRequestId';
const quoteReference = 'quoteReference';

const ipAddress = 'ipAddress';
const deviceFingerprint = 'deviceFingerprint';
const deviceFingerprintError = 'deviceFingerprintError';
const pointsTierVersionCode = 'pointsTierVersionCode';
const bookingChannel = HOTELS_BRAND_BOOKING_CHANNEL;

describe('when paying with adyen gateway', () => {
  it('constructs the expected payload', () => {
    const payload = createBookingPayload({
      formData: set({ ...formData }, 'payments.cash.creditCard', adyenCardData),
      quoteReference,
      ipAddress,
      deviceFingerprint,
      deviceFingerprintError,
      pointsTierVersionCode,
      clientRequestId,
      bookingChannel,
    });

    expect(payload).toEqual({
      bookingChannel,
      clientRequestId,
      travelArranger: {
        name: {
          title: formData.title,
          first: formData.firstName,
          last: formData.lastName,
        },
        email: formData.emailAddress,
        phone: formData.phoneNumber,
        businessNumber: formData.abn,
      },
      stays: [
        {
          quoteReference,
          leadGuest: {
            name: {
              title: formData.title,
              first: formData.firstName,
              last: formData.lastName,
            },
            qffNumber: formData.qffNumber,
          },
          specialRequests: formData.specialRequests,
        },
      ],
      payments: [
        {
          type: 'qff_account',
          pointsAmount: formData.payments.points.amount.toFixed(0),
          pointsTier: formData.payments.points.tierVersionCode,
          allocation: {
            amount: formData.payments.points.amountInCash.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'adyen_encrypted_credit_card',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.cash.creditCard.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.cash.creditCard.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.cash.creditCard.encryptedExpiryYear,
          encryptedSecurityCode: formData.payments.cash.creditCard.encryptedSecurityCode,
          tokenizedCreditCard: 'tokenizedCreditCard',
          holderName: 'holderName',
          cardType: 'cardType',
          allocation: {
            amount: formData.payments.cash.payableNow.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'adyen_encrypted_credit_card',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.cash.creditCard.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.cash.creditCard.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.cash.creditCard.encryptedExpiryYear,
          encryptedSecurityCode: formData.payments.cash.creditCard.encryptedSecurityCode,
          tokenizedCreditCard: 'tokenizedCreditCard',
          holderName: 'holderName',
          cardType: 'cardType',
          deferredTo: formData.payments.cash.payableLater.dueDate,
          allocation: {
            amount: formData.payments.cash.payableLater.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'hooroo_voucher',
          code: formData.payments.voucher.code,
          brand: BRAND_CODE,
          allocation: {
            amount: formData.payments.voucher.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'qantas_group_credit_voucher',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.travelPass.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.travelPass.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.travelPass.encryptedExpiryYear,
          allocation: {
            amount: formData.payments.travelPass.amount.toFixed(2),
            currency: 'AUD',
          },
        },
      ],
      requestorDetails: {
        ipAddress,
        deviceFingerprint,
        deviceFingerprintError,
      },
    });
  });
});

describe('when paying with voucher', () => {
  it('constructs the expected payload with the legacy voucher', () => {
    const payload = createBookingPayload({
      formData: set({ ...formData }, 'payments.cash.creditCard', adyenCardData),
      quoteReference,
      ipAddress,
      deviceFingerprint,
      deviceFingerprintError,
      pointsTierVersionCode,
      clientRequestId,
      bookingChannel,
    });

    expect(payload).toEqual({
      bookingChannel,
      clientRequestId,
      travelArranger: {
        name: {
          title: formData.title,
          first: formData.firstName,
          last: formData.lastName,
        },
        email: formData.emailAddress,
        phone: formData.phoneNumber,
        businessNumber: formData.abn,
      },
      stays: [
        {
          quoteReference,
          leadGuest: {
            name: {
              title: formData.title,
              first: formData.firstName,
              last: formData.lastName,
            },
            qffNumber: formData.qffNumber,
          },
          specialRequests: formData.specialRequests,
        },
      ],
      payments: [
        {
          type: 'qff_account',
          pointsAmount: formData.payments.points.amount.toFixed(0),
          pointsTier: formData.payments.points.tierVersionCode,
          allocation: {
            amount: formData.payments.points.amountInCash.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'adyen_encrypted_credit_card',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.cash.creditCard.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.cash.creditCard.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.cash.creditCard.encryptedExpiryYear,
          encryptedSecurityCode: formData.payments.cash.creditCard.encryptedSecurityCode,
          tokenizedCreditCard: 'tokenizedCreditCard',
          holderName: 'holderName',
          cardType: 'cardType',
          allocation: {
            amount: formData.payments.cash.payableNow.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'adyen_encrypted_credit_card',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.cash.creditCard.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.cash.creditCard.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.cash.creditCard.encryptedExpiryYear,
          encryptedSecurityCode: formData.payments.cash.creditCard.encryptedSecurityCode,
          tokenizedCreditCard: 'tokenizedCreditCard',
          holderName: 'holderName',
          cardType: 'cardType',
          deferredTo: formData.payments.cash.payableLater.dueDate,
          allocation: {
            amount: formData.payments.cash.payableLater.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'hooroo_voucher',
          code: formData.payments.voucher.code,
          brand: BRAND_CODE,
          allocation: {
            amount: formData.payments.voucher.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'qantas_group_credit_voucher',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.travelPass.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.travelPass.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.travelPass.encryptedExpiryYear,
          allocation: {
            amount: formData.payments.travelPass.amount.toFixed(2),
            currency: 'AUD',
          },
        },
      ],
      requestorDetails: {
        ipAddress,
        deviceFingerprint,
        deviceFingerprintError,
      },
    });
  });

  it('constructs the expected payload with vii voucher', () => {
    const payload = createBookingPayload({
      formData: set(
        {
          ...formData,
          payments: {
            ...formData.payments,
            voucher: {
              ...formData.payments.voucher,
              type: 'credit_voucher',
              pin: 'voucher-pin',
            },
          },
        },
        'payments.cash.creditCard',
        adyenCardData,
      ),
      quoteReference,
      ipAddress,
      deviceFingerprint,
      deviceFingerprintError,
      pointsTierVersionCode,
      clientRequestId,
      bookingChannel,
    });

    expect(payload).toEqual({
      bookingChannel,
      clientRequestId,
      travelArranger: {
        name: {
          title: formData.title,
          first: formData.firstName,
          last: formData.lastName,
        },
        email: formData.emailAddress,
        phone: formData.phoneNumber,
        businessNumber: formData.abn,
      },
      stays: [
        {
          quoteReference,
          leadGuest: {
            name: {
              title: formData.title,
              first: formData.firstName,
              last: formData.lastName,
            },
            qffNumber: formData.qffNumber,
          },
          specialRequests: formData.specialRequests,
        },
      ],
      payments: [
        {
          type: 'qff_account',
          pointsAmount: formData.payments.points.amount.toFixed(0),
          pointsTier: formData.payments.points.tierVersionCode,
          allocation: {
            amount: formData.payments.points.amountInCash.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'adyen_encrypted_credit_card',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.cash.creditCard.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.cash.creditCard.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.cash.creditCard.encryptedExpiryYear,
          encryptedSecurityCode: formData.payments.cash.creditCard.encryptedSecurityCode,
          tokenizedCreditCard: 'tokenizedCreditCard',
          holderName: 'holderName',
          cardType: 'cardType',
          allocation: {
            amount: formData.payments.cash.payableNow.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'adyen_encrypted_credit_card',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.cash.creditCard.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.cash.creditCard.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.cash.creditCard.encryptedExpiryYear,
          encryptedSecurityCode: formData.payments.cash.creditCard.encryptedSecurityCode,
          tokenizedCreditCard: 'tokenizedCreditCard',
          holderName: 'holderName',
          cardType: 'cardType',
          deferredTo: formData.payments.cash.payableLater.dueDate,
          allocation: {
            amount: formData.payments.cash.payableLater.amount.toFixed(2),
            currency: 'AUD',
          },
        },
        {
          type: 'credit_voucher',
          code: formData.payments.voucher.code,
          brand: BRAND_CODE,
          allocation: {
            amount: formData.payments.voucher.amount.toFixed(2),
            currency: 'AUD',
          },
          pin: 'voucher-pin',
        },
        {
          type: 'qantas_group_credit_voucher',
          apiVersion: ADYEN_API_VERSION,
          merchantId: ADYEN_MERCHANT_ID,
          encryptedCardNumber: formData.payments.travelPass.encryptedCardNumber,
          encryptedExpiryMonth: formData.payments.travelPass.encryptedExpiryMonth,
          encryptedExpiryYear: formData.payments.travelPass.encryptedExpiryYear,
          allocation: {
            amount: formData.payments.travelPass.amount.toFixed(2),
            currency: 'AUD',
          },
        },
      ],
      requestorDetails: {
        ipAddress,
        deviceFingerprint,
        deviceFingerprintError,
      },
    });
  });
});
