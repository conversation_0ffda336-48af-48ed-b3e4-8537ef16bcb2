import { getSearchGa4Results } from './getSearchGa4Results';

jest.mock('./searchSelectors', () => ({
  getSearchResults: jest.fn((state) => state.search.results),
}));

beforeEach(() => {
  jest.clearAllMocks();
});

const mockResults = [
  {
    property: { id: 1 },
    roomType: {
      name: 'room name',
    },
    offer: {
      charges: {
        total: {
          amount: '300.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
        strikethrough: {
          price: {
            amount: '400.00',
            currency: 'AUD',
          },
        },
      },
      allocationsAvailable: 5,
    },
  },
  {
    property: { id: 2 },
    roomType: {
      name: 'room name',
    },
    offer: {
      charges: {
        total: {
          amount: '300.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
      },
      allocationsAvailable: 10,
    },
  },
  {
    property: { id: 3 },
    roomType: {
      name: 'room name',
    },
    offer: {
      charges: {
        total: {
          amount: '300.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
        strikethrough: {
          price: {
            amount: '500.00',
            currency: 'AUD',
          },
        },
      },
      allocationsAvailable: 2,
    },
  },
  {
    property: { id: 4, hasOffer: true },
    roomType: {
      name: 'another room',
    },
    offer: {
      promotion: {
        name: 'Early Bird',
      },
      charges: {
        total: {
          amount: '150.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '150.00',
          currency: 'AUD',
        },
        strikethrough: '',
      },
      allocationsAvailable: 1,
    },
  },
];

describe('getSearchGa4Results', () => {
  it('returns the search results with strikethrough and correct hasOffer status', () => {
    const state = {
      search: {
        results: mockResults,
      },
    };

    const result = getSearchGa4Results(state);

    expect(result).toEqual([
      {
        property: { id: 1, hasOffer: false },
        roomType: {
          name: 'room name',
        },
        offer: {
          charges: {
            total: {
              amount: '300.00',
              currency: 'AUD',
            },
            totalCash: {
              amount: '0',
              currency: 'AUD',
            },
            strikethrough: {
              price: {
                amount: '400.00',
                currency: 'AUD',
              },
            },
          },
          allocationsAvailable: 5,
        },
      },
      {
        property: { id: 2, hasOffer: false },
        roomType: {
          name: 'room name',
        },
        offer: {
          charges: {
            total: {
              amount: '300.00',
              currency: 'AUD',
            },
            totalCash: {
              amount: '0',
              currency: 'AUD',
            },
            strikethrough: null,
          },
          allocationsAvailable: 10,
        },
      },
      {
        property: { id: 3, hasOffer: false },
        roomType: {
          name: 'room name',
        },
        offer: {
          charges: {
            total: {
              amount: '300.00',
              currency: 'AUD',
            },
            totalCash: {
              amount: '0',
              currency: 'AUD',
            },
            strikethrough: {
              price: {
                amount: '500.00',
                currency: 'AUD',
              },
            },
          },
          allocationsAvailable: 2,
        },
      },
      {
        property: { id: 4, hasOffer: true },
        roomType: {
          name: 'another room',
        },
        offer: {
          charges: {
            total: {
              amount: '150.00',
              currency: 'AUD',
            },
            totalCash: {
              amount: '150.00',
              currency: 'AUD',
            },
            strikethrough: null,
          },
          allocationsAvailable: 1,
        },
      },
    ]);
  });
});
