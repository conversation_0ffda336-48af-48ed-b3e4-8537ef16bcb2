import React from 'react';
import PointsPayCheckbox from './PointsPayCheckbox';
import { Checkbox, Text } from '@qga/roo-ui/components';
import { mountUtils } from 'test-utils';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import { setIsPointsPay } from 'store/ui/uiActions';
import { MIN_POINTS_AMOUNT } from 'config';
import { emitUserInteraction } from 'store/userEnvironment/userEnvironmentActions';

jest.mock('store/ui/uiSelectors');
jest.mock('store/user/userSelectors');
jest.mock('lib/oauth');

const render = (props = {}) => mountUtils(<PointsPayCheckbox {...props} />, { decorators: { store: true } });
const isPointsPay = true;

beforeEach(() => {
  getIsPointsPay.mockReturnValue(isPointsPay);
  useIsAuthenticated.mockReturnValue(false);
});

describe('when not logged in', () => {
  it('renders with expected props', () => {
    const { find } = render();
    expect(find(Checkbox)).toHaveProp({
      checked: true,
      disabled: false,
      autoComplete: 'off',
    });
  });

  it('renders with a custom font size', () => {
    const { find } = render({ size: 'xl' });
    expect(find(Checkbox)).toHaveProp({
      checked: true,
      disabled: false,
      autoComplete: 'off',
    });

    const textResults = find(Text);

    expect(textResults).toHaveLength(2);
    expect(textResults.first()).toHaveProp({
      fontSize: 'xl',
    });
  });

  it('does not dispatch setIsPointsPay(false)', () => {
    const { decorators } = render();
    expect(decorators.store.dispatch).not.toHaveBeenCalledWith(setIsPointsPay(false));
  });
});

describe('when logged in', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
  });

  describe('with a points balance under the minimum', () => {
    beforeEach(() => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.minus(1));
    });

    it('renders disabled', () => {
      const { find } = render();
      expect(find(Checkbox)).toHaveProp({
        disabled: true,
      });
    });

    it('dispatches setIsPointsPay(false)', () => {
      const { decorators } = render();
      expect(decorators.store.dispatch).toHaveBeenCalledWith(setIsPointsPay(false));
    });
  });

  describe('with a points balance at the minimum', () => {
    beforeEach(() => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT);
    });

    it('renders enabled', () => {
      const { find } = render();
      expect(find(Checkbox)).toHaveProp({
        disabled: false,
      });
    });

    it('does not dispatch setIsPointsPay(false)', () => {
      const { decorators } = render();
      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(setIsPointsPay(false));
    });
  });
});

describe('toggling the state', () => {
  it('dispatches the setIsPointsPay action', () => {
    const { find, decorators } = render();
    find(Checkbox).prop('onChange')();
    expect(decorators.store.dispatch).toHaveBeenCalledWith(setIsPointsPay(!isPointsPay));
  });

  it('emits a gtm event when clicked', () => {
    const { find, decorators } = render();
    const { dispatch } = decorators.store;

    find(Checkbox).prop('onChange')();
    expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: emitUserInteraction.type }));
  });
});
