import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import React, { useCallback, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Checkbox, Box, Label, Text, Flex } from '@qga/roo-ui/components';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { setIsPointsPay } from 'store/ui/uiActions';
import { FormatNumber } from 'components/formatters';
import { useDataLayer } from 'hooks/useDataLayer';
import { MIN_POINTS_AMOUNT } from 'config';
import { updateQuery } from 'store/propertyAvailability/propertyAvailabilityActions';
import { PAYMENT_METHODS } from 'lib/enums/payment';
import { useIsAuthenticated } from 'lib/oauth';

const StyledLabel = styled(Label)`
  line-height: inherit;
`;

const PointsPayCheckBox = ({ showPointsDescription, size, ...rest }) => {
  const dispatch = useDispatch();
  const isPointsPay = useSelector(getIsPointsPay);
  const pointsBalance = useSelector(getPointsBalance);
  const isAuthenticated = useIsAuthenticated();
  const { emitInteractionEvent } = useDataLayer();
  const isDisabled = isAuthenticated && pointsBalance.lessThan(MIN_POINTS_AMOUNT) ? true : false;
  const payWith = PAYMENT_METHODS.POINTS;

  const onChange = useCallback(() => {
    dispatch(setIsPointsPay(!isPointsPay));
    if (!isPointsPay) dispatch(updateQuery({ payWith: payWith }));

    emitInteractionEvent({ type: 'Points and Pay Slider', value: `Points + Pay Opted ${isPointsPay ? 'Out' : 'In'}` });
  }, [dispatch, emitInteractionEvent, isPointsPay, payWith]);

  useEffect(() => {
    if (isAuthenticated && isDisabled) dispatch(setIsPointsPay(false));
  }, [dispatch, isAuthenticated, isDisabled]);

  return (
    <Flex flexDirection="column" justifyContent="center" {...rest}>
      <Box>
        <StyledLabel mb={0}>
          <Flex alignItems="center" justifyContent={['center', 'flex-end']}>
            <Checkbox
              name="is-points-pay"
              checked={isPointsPay}
              onChange={onChange}
              disabled={isDisabled}
              data-testid="points-pay-checkbox"
              autoComplete="off"
            />
            <Text color={isDisabled ? 'greys.dusty' : 'greys.charcoal'} fontSize={size}>
              Use Points + Pay
            </Text>
          </Flex>
        </StyledLabel>
      </Box>
      <Box display={showPointsDescription ? 'block' : ['block', 'none']}>
        <Text color="greys.dusty" fontSize="sm">
          Minimum <FormatNumber number={MIN_POINTS_AMOUNT} />
          pts required
        </Text>
      </Box>
    </Flex>
  );
};

PointsPayCheckBox.propTypes = {
  showPointsDescription: PropTypes.bool,
  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.array]),
};

PointsPayCheckBox.defaultProps = {
  showPointsDescription: true,
  size: 'base',
};

export default PointsPayCheckBox;
