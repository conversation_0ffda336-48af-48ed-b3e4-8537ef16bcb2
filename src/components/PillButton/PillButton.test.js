import React from 'react';
import PillButton from './PillButton';
import { mountUtils } from 'test-utils';

const render = (props) =>
  mountUtils(
    <PillButton {...props}>
      <div data-testid="test-div" />
    </PillButton>,
  );

describe('renders the <PillButton /> component', () => {
  it('renders the pill button children', () => {
    const { findByTestId } = render();
    expect(findByTestId('test-div')).toExist();
  });

  it('renders the pill button props', () => {
    const { find } = render({ foo: 'bar' });
    expect(find('PillButton')).toHaveProp({
      foo: 'bar',
    });
  });
});
