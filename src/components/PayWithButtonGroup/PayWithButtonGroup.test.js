import React from 'react';
import { PayWithButtonGroup as RooPayWithButtonGroup } from '@qga/roo-ui/components';
import { mountUtils } from 'test-utils';
import convertPayWithQueryParams from 'lib/search/convertPayWithQueryParams';
import PayWithButtonGroup from './PayWithButtonGroup';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import usePointsConverters from 'hooks/points/usePointsConverters';

jest.mock('lib/search/convertPayWithQueryParams');
jest.mock('store/ui/uiSelectors');
jest.mock('hooks/points/usePointsConverters');

const isPointsPay = true;
const convertCashToPoints = jest.fn();
const convertPointsToCash = jest.fn();

describe('<PayWithButtonGroup />', () => {
  let props;
  const convertedPayWithParams = { converted: true };

  const render = () => mountUtils(<PayWithButtonGroup {...props} />, { decorators: { store: true, theme: true } });

  beforeEach(() => {
    props = {
      payWith: 'bitcoin',
      updateQuery: jest.fn(),
      isDisabled: true,
      size: 'lg',
    };

    convertPayWithQueryParams.mockReturnValue(convertedPayWithParams);
    usePointsConverters.mockReturnValue({ convertCashToPoints, convertPointsToCash });
    getIsPointsPay.mockReturnValue(isPointsPay);
  });

  it('renders a ButtonGroup with expected props', () => {
    const { find } = render();

    expect(find(RooPayWithButtonGroup)).toHaveProp({
      name: 'payWith',
      value: props.payWith,
      onChange: expect.any(Function),
      size: 'lg',
    });
  });

  it('calls updateQuery with convertedPayWithParams on change of button group', () => {
    const newPaymentMethod = 'cash';
    render().find(RooPayWithButtonGroup).prop('onChange')(newPaymentMethod);
    expect(convertPayWithQueryParams).toHaveBeenCalledWith({
      convertCashToPoints: expect.any(Function),
      convertPointsToCash: expect.any(Function),
      payWith: newPaymentMethod,
      query: props.query,
    });
    expect(props.updateQuery).toHaveBeenCalledWith(convertedPayWithParams);
  });
});
