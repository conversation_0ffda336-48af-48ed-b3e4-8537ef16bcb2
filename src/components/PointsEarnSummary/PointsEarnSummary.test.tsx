import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import PointsEarnSummary from './PointsEarnSummary';
import PointsEarnDisplay from 'components/PointsEarnDisplay';
import * as config from 'config';

jest.mock('components/PointsEarnDisplay');
jest.mock('config');

const render = (props) => mountUtils(<PointsEarnSummary {...props} />);

beforeEach(() => {
  jest.clearAllMocks();
  Object.assign(config, jest.requireActual('config'));
});

describe('when POINTS_EARN_ENABLED flag is on', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: true });
  });

  describe('when total points is provided without base', () => {
    beforeEach(() => {
      mocked(PointsEarnDisplay).mockReturnValue('1,200');
    });

    it('return the correct text', () => {
      const { find } = render({ total: 1200 });
      expect(find('Text[data-testid="points-earn-summary-text"]').text()).toEqual('Earn 1,200 PTS^');
    });

    it('renders PointsEarnDisplay with the expected props', () => {
      const { find } = render({ total: 1200, fontSize: 'base' });
      expect(find(PointsEarnDisplay)).toHaveProp({ total: 1200, fontSize: 'base' });
    });

    it('does not render Icon', () => {
      const { find } = render({ total: 1200 });
      expect(find('Icon[name="roo"]').exists()).toEqual(false);
    });
  });

  describe('when total points is provided with base', () => {
    beforeEach(() => {
      mocked(PointsEarnDisplay).mockReturnValue('(1,200) 600');
    });

    it('return the correct text', () => {
      const { find } = render({ total: 1200, base: 600 });
      expect(find('Text[data-testid="points-earn-summary-text"]').text()).toEqual('Earn (1,200) 600 PTS^');
    });

    it('renders PointsEarnDisplay with the expected props', () => {
      const { find } = render({ total: 1200, base: 600 });
      expect(find(PointsEarnDisplay)).toHaveProp({ total: 1200, base: 600 });
    });

    it('does not render Icon', () => {
      const { find } = render({ total: 1200 });
      expect(find('Icon[name="roo"]').exists()).toEqual(false);
    });
  });

  describe('with no total points', () => {
    it('returns no text', () => {
      const { find } = render({ total: 0 });
      expect(find('Text').exists()).toEqual(false);
    });
  });

  describe('with luxury inclusions', () => {
    it('renders Icon with the expected props', () => {
      const { find } = render({ total: 1200, luxuryInclusions: true });
      expect(find('Icon[name="roo"]')).toHaveProp({ name: 'roo', mr: '4px', mt: '3px', color: 'brand.primary', size: 20 });
    });
  });
});

describe('when POINTS_EARN_ENABLED flag is off', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: false });
  });

  it('returns no text', () => {
    const { find } = render({ total: 1200, base: 600 });
    expect(find('Text').exists()).toEqual(false);
  });
});
