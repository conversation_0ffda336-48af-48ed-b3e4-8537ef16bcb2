import React, { Fragment, useRef } from 'react';
import * as sessionStorage from 'lib/browser/sessionStorage';
import styled from '@emotion/styled';
import map from 'lodash/map';
import includes from 'lodash/includes';
import { themeGet } from 'styled-system';
import { Card } from '@qga/roo-ui/components';
import isNil from 'lodash/isNil';
import SearchResultHighlighter from 'components/ListSearchPage/SearchResultHighlighter';
import SearchResultDetail from './SearchResultDetail';
import { useSelector } from 'react-redux';
import { getQueryFeaturedPropertyId, getQueryUtmSource, getQueryUtmCampaign, getQueryLocation } from 'store/router/routerSelectors';
import ExpandedClickableArea from 'components/ExpandedClickableArea';
import { VALID_CAMPAIGN_SOURCES } from 'lib/enums/property';
import { Offer, Property, RoomType } from '../../../types/property';
import { getCampaignDefaultSash } from 'store/campaign/campaignSelectors';
import useViewPromotionEvent from 'hooks/useViewPromotionEvent';
import useSelectPromotionEvent from 'hooks/useSelectPromotionEvent';
import useSelectItemEvent from 'hooks/useSelectItemEvent';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getRoomAvailabilityMessageStandard } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

type Props = {
  index: number;
  result: {
    property: Property;
    offer: Offer;
    roomType: RoomType;
  };
};

const validateUtm = (selectedUtm) => {
  if (typeof selectedUtm === 'string') {
    return selectedUtm;
  } else {
    const utmResult: string[] = [];
    map(selectedUtm, (utm: string) => {
      if (includes(VALID_CAMPAIGN_SOURCES, utm)) {
        utmResult.push(utm);
      }
    });
    if (utmResult.length > 0) {
      return utmResult[0];
    } else {
      return undefined;
    }
  }
};

const StyledCard = styled(Card)`
  padding: 0;
  position: relative;
  box-shadow: ${themeGet('shadows.heavy')};
  transition: all 0.1s linear;
  &:hover {
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  }
`;

const isHighlighted = (featuredId, propertyId) => {
  if ([featuredId, propertyId].some(isNil)) return false;
  return Number(featuredId) === Number(propertyId);
};

const SearchResult = React.memo(({ result, index }: Props) => {
  const ref = useRef<HTMLDivElement>(null);
  const location = useSelector(getQueryLocation);
  const featuredPropertyId = useSelector(getQueryFeaturedPropertyId);
  const selectCampaign = useSelector(getQueryUtmCampaign);
  const utmCampaign = validateUtm(selectCampaign);
  const selectSource = useSelector(getQueryUtmSource);
  const utmSource = validateUtm(selectSource);
  const highlighted = isHighlighted(featuredPropertyId, result.property.id);
  const referrerProp = { referrer: utmCampaign || utmSource };

  const isClassic = result.offer.type === 'classic';
  const globalCampaignDefaultSash = useSelector(getCampaignDefaultSash);
  const useGlobalCampaignDefaultSash = globalCampaignDefaultSash && !isClassic;
  const promotionName = useGlobalCampaignDefaultSash ? globalCampaignDefaultSash : result.offer.promotion?.name;
  const promotion = { name: promotionName, slot: 'search_result', assetName: result.property.name };
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const { fireSelectPromotionEvent } = useSelectPromotionEvent({ promotion });
  const { fireSelectItemEvent } = useSelectItemEvent();
  useViewPromotionEvent({ ref, promotion });

  const roomsAllocationsAvailable = result.offer.allocationsAvailable ?? 0;
  const showMessage = GetShowCtaMessage(roomsAllocationsAvailable);
  const availableRoomsMessage = showMessage ? getRoomAvailabilityMessageStandard(roomsAllocationsAvailable) : '';
  const { isPriceStrikethrough } = usePriceStrikethrough();

  const sendEvents = () => {
    const offerToDispatch = JSON.parse(JSON.stringify(result.offer));
    if (!isPriceStrikethrough && offerToDispatch?.charges?.strikethrough) {
      delete offerToDispatch.charges.strikethrough;
    }
    fireSelectPromotionEvent();
    fireSelectItemEvent({
      listName: `Hotels in ${location}`,
      type: 'list',
      property: result.property,
      offer: offerToDispatch,
      roomType: result.roomType,
      ctaMessageCategory: showMessage ? 'available rooms' : '',
      ctaMessage: availableRoomsMessage,
    });
  };

  const setScrolledHeight = () => {
    sessionStorage.set('scrolledHeight', window.scrollY);
  };

  const handleClick = () => {
    sendEvents();
    setScrolledHeight();
  };

  return (
    <StyledCard data-testid="search-result" mb={[5, 6]} borderTop={isMobile ? '1px solid #D9D9D9' : 'none'} ref={ref} onClick={handleClick}>
      {highlighted && (
        <SearchResultHighlighter {...referrerProp}>
          <ExpandedClickableArea position="relative">
            <SearchResultDetail result={result} cardIndex={index} />
          </ExpandedClickableArea>
        </SearchResultHighlighter>
      )}
      {!highlighted && (
        <Fragment>
          <ExpandedClickableArea position="relative">
            <SearchResultDetail result={result} cardIndex={index} />
          </ExpandedClickableArea>
        </Fragment>
      )}
    </StyledCard>
  );
});

export default SearchResult;
