import React from 'react';
import Slider from 'rc-slider';
import PointsPaySlider from './PointsPaySlider';
import { mountUtils } from 'test-utils';

jest.mock('rc-slider', () => () => null);

const render = (props) => mountUtils(<PointsPaySlider {...props} />);

const onChange = jest.fn();
const minCashAmount = 0;
const maxCashAmount = 300;
const initialCashAmount = 50;

it('renders the PointsPaySlider with the expected props', () => {
  const { find } = render({ minCashAmount, maxCashAmount, initialCashAmount, onChange });
  expect(find(Slider)).toHaveProp({
    min: minCashAmount,
    max: maxCashAmount,
    defaultValue: initialCashAmount,
    onChange,
    step: 0.01,
  });
});
