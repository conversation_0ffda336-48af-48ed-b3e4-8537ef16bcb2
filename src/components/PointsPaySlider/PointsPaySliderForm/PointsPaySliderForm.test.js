import React from 'react';
import { Decimal } from 'decimal.js';
import { mountUtils } from 'test-utils';
import PointsPaySlider from 'components/PointsPaySlider';
import PointsPaySliderForm from './PointsPaySliderForm';
import usePointsConverters from 'hooks/points/usePointsConverters';
import formatNumber from 'lib/formatters/formatNumber';
import { act } from 'react-dom/test-utils';
import { usePointsPaySlider } from 'lib/hooks/slider';
import { getLuxeInstanceId, getLuxePointsLevels } from 'store/pointsBurnLuxe/pointsBurnLuxeSelectors';
import { getIsExclusive } from 'store/property/propertySelectors';

jest.mock('components/PointsPaySlider');
jest.mock('lib/hooks/slider');
jest.mock('hooks/points/usePointsConverters');
jest.mock('lodash/debounce', () => (fn) => fn);
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/pointsBurnLuxe/pointsBurnLuxeSelectors');
jest.mock('store/property/propertySelectors');

mountUtils.mockComponent('PointsPaySlider');
jest.useFakeTimers();

const POINTS_CONVERSION_MULTIPLIER = 1000;

let baseProps;

const render = (props) =>
  mountUtils(
    <PointsPaySliderForm {...baseProps} {...props}>
      <div data-testid="children">CHILDREN</div>
    </PointsPaySliderForm>,
    { decorators: { store: true, theme: true } },
  );

const onChange = jest.fn();
const pointsAmount = new Decimal(10000);
const pointsAmountInCash = new Decimal(50);
const cashAmount = new Decimal(100);
const minCashAmount = new Decimal(10);
const maxCashAmount = new Decimal(200);
const initialCashAmount = new Decimal(50);

const sliderProps = {
  cashAmount,
  initialCashAmount,
  maxCashAmount,
  minCashAmount,
  onChange,
  pointsAmount,
  pointsAmountInCash,
};

const convertCashToPoints = ({ cash }) => new Decimal(cash).times(POINTS_CONVERSION_MULTIPLIER);
const convertPointsToCash = ({ points }) => new Decimal(points).dividedBy(POINTS_CONVERSION_MULTIPLIER);

beforeEach(() => {
  jest.clearAllMocks();
  getLuxeInstanceId.mockReturnValue('123');
  getIsExclusive.mockReturnValue(false);
  baseProps = {
    initialPointsAmount: new Decimal(5000),
    totalCashAmount: new Decimal(250),
    updatePointsAndCash: jest.fn(),
  };

  PointsPaySlider.mockReturnValue(null);

  usePointsPaySlider.mockReturnValue(sliderProps);

  usePointsConverters.mockReturnValue({
    convertCashToPoints,
    convertPointsToCash,
  });
});

describe('PointsPaySliderForm()', () => {
  describe('when the offer is a standard offer', () => {
    it('calls usePointsPaySlider with expected arguments', () => {
      render();
      expect(usePointsPaySlider).toHaveBeenCalledWith({
        totalCash: { amount: baseProps.totalCashAmount },
        initialPointsAmount: baseProps.initialPointsAmount,
        isLuxeOffer: false,
      });
    });

    it('renders the PointsPaySlider', () => {
      const { find } = render();
      expect(find('PointsPaySlider')).toHaveProp({
        minCashAmount: minCashAmount.toNumber(),
        maxCashAmount: maxCashAmount.toNumber(),
        initialCashAmount: initialCashAmount.toNumber(),
        onChange,
      });
    });

    it('renders the formatted points input', () => {
      const { findByTestId } = render();
      expect(findByTestId('points-input')).toHaveProp({ value: '10,000' });
    });

    it('renders the formatted cash input', () => {
      const { findByTestId } = render();
      expect(findByTestId('cash-input')).toHaveProp({ value: '100.00' });
    });

    it('calls updatePointsAndCash', () => {
      render();
      expect(baseProps.updatePointsAndCash).toHaveBeenCalledWith({
        points: { amount: pointsAmount, amountInCash: pointsAmountInCash },
        cash: { amount: cashAmount },
      });
    });
  });

  describe('when the offer is a luxe offer', () => {
    beforeEach(() => {
      getLuxePointsLevels.mockReturnValue(['luxe-tiers']);
    });

    it('calls usePointsPaySlider with expected arguments', () => {
      render({ offerInstanceId: '123' });

      expect(usePointsPaySlider).toHaveBeenCalledWith({
        totalCash: { amount: baseProps.totalCashAmount },
        initialPointsAmount: baseProps.initialPointsAmount,
        isLuxeOffer: true,
      });
    });

    it('calls usePointsConverters with luxe tiers', () => {
      render({ offerInstanceId: '123' });

      expect(usePointsConverters).toHaveBeenCalledWith(['luxe-tiers']);
    });
  });

  describe('when the offer is an exclusive offer', () => {
    beforeEach(() => {
      getLuxePointsLevels.mockReturnValue(['luxe-tiers']);
      getIsExclusive.mockReturnValue(true);
    });

    it('calls usePointsPaySlider with expected arguments', () => {
      render({ offerInstanceId: '456' });

      expect(usePointsPaySlider).toHaveBeenCalledWith({
        totalCash: { amount: baseProps.totalCashAmount },
        initialPointsAmount: baseProps.initialPointsAmount,
        isLuxeOffer: true,
      });
    });

    it('calls usePointsConverters with luxe tiers', () => {
      render({ offerInstanceId: '456' });

      expect(usePointsConverters).toHaveBeenCalledWith(['luxe-tiers']);
    });
  });

  describe('if pointsAmount changes', () => {
    it('calls updatePointsAndCash', () => {
      const { wrapper } = render();
      const newPointsAmount = new Decimal(7000);

      usePointsPaySlider.mockReturnValue({
        pointsAmount: newPointsAmount,
        pointsAmountInCash,
        cashAmount,
        minCashAmount,
        maxCashAmount,
        initialCashAmount,
        onChange,
      });

      //force a re-render
      wrapper.setProps({ ...baseProps, foo: 'bar' });

      expect(baseProps.updatePointsAndCash).toHaveBeenCalledWith({
        points: { amount: newPointsAmount, amountInCash: pointsAmountInCash },
        cash: { amount: cashAmount },
      });
    });
  });

  describe('if cashAmount changes', () => {
    it('calls updatePointsAndCash', () => {
      const { wrapper } = render();
      const newCashAmount = new Decimal(40);

      usePointsPaySlider.mockReturnValue({
        pointsAmount,
        pointsAmountInCash,
        cashAmount: newCashAmount,
        minCashAmount,
        maxCashAmount,
        initialCashAmount,
        onChange,
      });

      //force a re-render
      wrapper.setProps({ ...baseProps, foo: 'bar' });

      expect(baseProps.updatePointsAndCash).toHaveBeenCalledWith({
        points: { amount: pointsAmount, amountInCash: pointsAmountInCash },
        cash: { amount: newCashAmount },
      });
    });
  });

  describe('if updatePointsAndCash changes', () => {
    it('calls new updatePointsAndCash', () => {
      const { wrapper } = render();

      usePointsPaySlider.mockReturnValue({
        pointsAmount,
        pointsAmountInCash,
        cashAmount,
        minCashAmount,
        maxCashAmount,
        initialCashAmount,
        onChange,
      });

      const updatePointsAndCash = jest.fn();
      wrapper.setProps({ ...baseProps, updatePointsAndCash });

      expect(updatePointsAndCash).toHaveBeenCalledWith({
        points: { amount: pointsAmount, amountInCash: pointsAmountInCash },
        cash: { amount: cashAmount },
      });
    });
  });

  describe('when entering a formatted cash amount', () => {
    describe('on blur of the cash input', () => {
      it('calls slider onChange with the expected cash amount', () => {
        const { findByTestId } = render();
        const cashInput = findByTestId('cash-input');
        const cashAmount = new Decimal(50);

        act(() => {
          cashInput.simulate('change', { target: { value: '$50.00' } });
        });
        act(() => {
          cashInput.simulate('blur');
        });

        expect(onChange).toHaveBeenCalledWith(cashAmount);
      });
    });

    describe('on keyUp of cash input with enter key', () => {
      it('calls slider onChange with the expected cash amount', () => {
        const { findByTestId } = render();
        const cashInput = findByTestId('cash-input');
        const cashAmount = new Decimal(50);

        act(() => {
          cashInput.simulate('change', { target: { value: '$50.00' } });
        });
        act(() => {
          cashInput.simulate('keyUp', { keyCode: 13 });
        });

        expect(onChange).toHaveBeenCalledWith(cashAmount);
      });
    });

    describe('on keyUp of cash input with other key', () => {
      it('does not call slider onChange', () => {
        const { findByTestId } = render();
        const cashInput = findByTestId('cash-input');

        act(() => {
          cashInput.simulate('keyUp', { keyCode: 1 });
        });

        expect(onChange).not.toHaveBeenCalled();
      });
    });

    describe('when the cash amount is less than the minimum cash amount', () => {
      it('calls slider onChange with the cash amount increased to the minimum cash amount', () => {
        const { findByTestId } = render();
        const cashInput = findByTestId('cash-input');
        const cashAmount = new Decimal(minCashAmount).minus(1);

        act(() => {
          cashInput.simulate('change', { target: { value: cashAmount.toString() } });
        });
        act(() => {
          cashInput.simulate('blur');
        });

        expect(onChange).toHaveBeenCalledWith(new Decimal(minCashAmount));
      });
    });

    describe('when the cash amount is greater then the maximum cash amount', () => {
      it('calls slider onChange with the cash amount decreased to the maximum cash amount ', () => {
        const { findByTestId } = render();
        const cashInput = findByTestId('cash-input');
        const cashAmount = new Decimal(maxCashAmount).plus(1);

        act(() => {
          cashInput.simulate('change', { target: { value: cashAmount.toString() } });
        });
        act(() => {
          cashInput.simulate('blur');
        });

        expect(onChange).toHaveBeenCalledWith(new Decimal(maxCashAmount));
      });
    });
  });

  describe('when entering a formatted points amount', () => {
    describe('onBlur of the points Input', () => {
      it('calls slider onChange with the expected cash amount', () => {
        const { findByTestId } = render();
        const { totalCashAmount } = baseProps;
        const pointsInput = findByTestId('points-input');
        const pointsAmount = convertCashToPoints({ cash: totalCashAmount.minus(minCashAmount) });
        const cashAmountForPoints = convertPointsToCash({ points: pointsAmount });

        act(() => {
          pointsInput.simulate('change', { target: { value: formatNumber({ number: pointsAmount.toNumber(), decimalPlaces: 0 }) } });
        });
        act(() => {
          pointsInput.simulate('blur');
        });

        expect(onChange).toHaveBeenCalledWith(totalCashAmount.minus(cashAmountForPoints));
      });
    });

    describe('onKeyUp of the points Input with enter key', () => {
      it('calls slider onChange with the expected cash amount', () => {
        const { findByTestId } = render();
        const { totalCashAmount } = baseProps;
        const pointsInput = findByTestId('points-input');
        const pointsAmount = convertCashToPoints({ cash: totalCashAmount.minus(minCashAmount) });
        const cashAmountForPoints = convertPointsToCash({ points: pointsAmount });

        act(() => {
          pointsInput.simulate('change', { target: { value: pointsAmount.toString() } });
        });
        act(() => {
          pointsInput.simulate('keyUp', { keyCode: 13 });
        });

        expect(onChange).toHaveBeenCalledWith(totalCashAmount.minus(cashAmountForPoints));
      });
    });

    describe('onKeyUp of the points Input with other key', () => {
      it('does not call slider onChange', () => {
        const { findByTestId } = render();
        const pointsInput = findByTestId('points-input');
        pointsInput.simulate('keyUp', { keyCode: 1 });

        expect(onChange).not.toHaveBeenCalled();
      });
    });

    describe('when updating the points input', () => {
      describe('when the points amount is zero', () => {
        it('ensures the points input remains at zero', () => {
          const { findByTestId, wrapper } = render();
          const pointsInput = findByTestId('points-input');

          act(() => {
            pointsInput.simulate('change', { target: { value: '0' } });
          });
          act(() => {
            pointsInput.simulate('blur');
          });

          wrapper.update();

          expect(pointsInput.prop('value')).toEqual('10,000');
        });
      });

      describe('when the points amount is above zero, but less than the minimum points amount', () => {
        it('resets the points input to the minimum points value', () => {
          const { findByTestId, wrapper } = render();
          const initialPointsInput = findByTestId('points-input');

          act(() => {
            initialPointsInput.simulate('change', { target: { value: '10' } });
          });
          act(() => {
            initialPointsInput.simulate('blur');
          });

          wrapper.update();

          const updatedPointsInput = findByTestId('points-input');

          expect(updatedPointsInput.prop('value')).toEqual('5,000');
        });
      });

      describe('when the points amount is above the max value', () => {
        it('resets the points input to the max points value', () => {
          const { findByTestId, wrapper } = render();
          const initialPointsInput = findByTestId('points-input');

          act(() => {
            initialPointsInput.simulate('change', { target: { value: '99,999' } });
          });
          act(() => {
            initialPointsInput.simulate('blur');
          });

          wrapper.update();

          const updatedPointsInput = findByTestId('points-input');

          expect(updatedPointsInput.prop('value')).toEqual('10,000');
        });
      });
    });

    describe('when the points causes the cash amount to be less than the minimum cash amount', () => {
      it('calls slider onChange with the cash amount increased to the minimum cash amount', () => {
        const { findByTestId } = render();
        const { totalCashAmount } = baseProps;
        const pointsInput = findByTestId('points-input');
        const pointsAmount = convertCashToPoints({ cash: totalCashAmount.minus(minCashAmount) }).plus(1000);

        act(() => {
          pointsInput.simulate('change', { target: { value: pointsAmount.toString() } });
        });
        act(() => {
          pointsInput.simulate('blur');
        });

        expect(onChange).toHaveBeenCalledWith(new Decimal(minCashAmount));
      });
    });

    describe('when the points causes the cash amount to be greater then the maximum cash amount', () => {
      it('calls slider onChange with the cash amount decreased to the maximum cash amount ', () => {
        const { findByTestId } = render();
        const { totalCashAmount } = baseProps;
        const pointsInput = findByTestId('points-input');
        const pointsAmount = convertCashToPoints({ cash: totalCashAmount.minus(maxCashAmount) }).minus(1000);

        act(() => {
          pointsInput.simulate('change', { target: { value: pointsAmount.toString() } });
        });
        act(() => {
          pointsInput.simulate('blur');
        });

        expect(onChange).toHaveBeenCalledWith(new Decimal(maxCashAmount));
      });
    });
  });
});
