import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import pick from 'lodash/pick';
import { useDataLayer } from 'hooks/useDataLayer';
import { Flex, Button } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import stringifyQueryValues from 'lib/search/stringifyQueryValues';
import { getFullKnownQuery } from 'store/router/routerSelectors';
import AppLink from 'components/AppLink';
import { getAllAvailableOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { MIN_POINTS_AMOUNT } from 'config';
import MinimumPointsDialog from 'components/CheckoutPage/BookingErrorDialogs/MinimumPointsDialog';
import { useRouter } from 'next/router';
import { addToCart } from 'store/checkout/checkoutActions';
import { getProperty } from 'store/property/propertySelectors';
import { getAvailableRoomTypes } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';
import useSelectPromotionEvent from 'hooks/useSelectPromotionEvent';
import isNumber from 'lodash/isNumber';
import { getRoomAvailabilityMessageForOffer } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import useCtaClickEvent from 'hooks/useCtaClickEvent';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

const ButtonLink = styled(Button)`
  flex-grow: 1;
  width: 100%;
  font-size: ${themeGet('fontSizes.base')};
  padding-top: 10px;
  padding-bottom: 10px;

  ${mediaQuery.minWidth.md} {
    padding: ${themeGet('space.2')} ${themeGet('space.6')};
    flex-grow: initial;
  }

  &:hover {
    color: ${themeGet('colors.white')};
  }
`;

ButtonLink.displayName = 'ButtonLink';

const checkoutQueryString = ({ query, roomTypeId, propertyId, offerId, payWith, initialCash, exclusiveOffer, ss_action }) => {
  const baseCheckoutQuery = {
    roomTypeId,
    propertyId,
    offerId,
    ...pick(query, ['checkIn', 'checkOut', 'adults', 'children', 'infants', 'travelType']),
    ss_action,
  };

  return stringifyQueryValues({
    ...baseCheckoutQuery,
    initialCash: isNumber(initialCash) ? initialCash : undefined,
    payWith,
    exclusiveOffer,
  });
};

const OfferCheckoutLink = ({
  propertyId,
  roomTypeId,
  offerId,
  offerName,
  initialCash = undefined,
  payWith = undefined,
  roomName,
  children,
  exclusiveOffer,
  ...rest
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const property = useSelector(getProperty);
  const query = useSelector(getFullKnownQuery);
  const pointsConversion = useSelector(getPointsConversion);
  const isRebooked = router.query.ss_action === 'rebook';
  const checkoutUrl = `/checkout?${checkoutQueryString({
    query,
    initialCash,
    roomTypeId,
    propertyId,
    offerId,
    payWith: payWith || query.payWith,
    exclusiveOffer,
    ss_action: router.query.ss_action,
  })}`;

  const { emitInteractionEvent } = useDataLayer();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const availableOffers = useSelector(getAllAvailableOffers);
  const offer = availableOffers[offerId];
  const roomTypes = useSelector(getAvailableRoomTypes);
  const roomType = roomTypes.find((r) => r.id === roomTypeId);
  const promotion = { name: offer?.promotion?.name, slot: 'room_offer', assetName: property.name };
  const { fireSelectPromotionEvent } = useSelectPromotionEvent({ promotion });
  const allocationsAvailable = offer?.allocationsAvailable;
  const showCtaMessage = GetShowCtaMessage(offer?.allocationsAvailable);
  const ctaMessage = showCtaMessage ? getRoomAvailabilityMessageForOffer(allocationsAvailable) : '';
  const ctaMessageCategory = showCtaMessage ? 'available rooms' : '';
  const { isPriceStrikethrough } = usePriceStrikethrough();
  const { ctaClickEvent } = useCtaClickEvent();

  const handleOnClick = useCallback(
    (event) => {
      emitInteractionEvent({ type: 'Select Offer Button', value: `${offerName}` });
      fireSelectPromotionEvent();
      ctaClickEvent({
        itemText: event.target.textContent,
        itemType: 'button',
        url: checkoutUrl,
      });
      let offerToDispatch = JSON.parse(JSON.stringify(offer));
      if (!isPriceStrikethrough && offerToDispatch?.charges?.strikethrough) {
        delete offerToDispatch.charges.strikethrough;
      }

      dispatch(
        addToCart({
          ctaMessage,
          ctaMessageCategory,
          initialCash,
          isRebooked,
          offer: offerToDispatch,
          pointsConversion,
          property,
          query,
          roomType,
        }),
      );

      if (offer?.charges?.total) {
        const total = offer.charges.total;
        if (total.currency === 'PTS' && total.amount < MIN_POINTS_AMOUNT) {
          setIsDialogOpen(true);
          event.preventDefault();
        }
      }
    },
    [
      emitInteractionEvent,
      offerName,
      fireSelectPromotionEvent,
      dispatch,
      ctaMessage,
      ctaMessageCategory,
      initialCash,
      isRebooked,
      offer,
      pointsConversion,
      property,
      query,
      roomType,
      ctaClickEvent,
      checkoutUrl,
      isPriceStrikethrough,
    ],
  );

  const handleDialogContinue = useCallback(() => {
    router.push(checkoutUrl);
  }, [checkoutUrl, router]);

  const handleDialogChange = useCallback(() => {
    setIsDialogOpen(false);
  }, []);

  return (
    <Flex justifyContent="flex-start" mt={3} {...rest} pb={4}>
      <MinimumPointsDialog
        isOpen={isDialogOpen}
        onContinueClick={handleDialogContinue}
        onChangeClick={handleDialogChange}
        roomTypeId={roomTypeId}
        offerId={offerId}
      />
      <ButtonLink
        as={AppLink}
        to={checkoutUrl}
        variant="primary"
        data-testid="offer-checkout-link"
        onClick={handleOnClick}
        aria-label={`Select ${roomName}, ${offerName}`}
      >
        {children}
      </ButtonLink>
    </Flex>
  );
};

OfferCheckoutLink.propTypes = {
  propertyId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  roomTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerName: PropTypes.string.isRequired,
  initialCash: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  payWith: PropTypes.string,
  roomName: PropTypes.string.isRequired,
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func, PropTypes.string]),
  exclusiveOffer: PropTypes.bool,
};

OfferCheckoutLink.defaultProps = {
  initialCash: null,
  payWith: null,
  children: 'Select',
  exclusiveOffer: false,
};

export default OfferCheckoutLink;
