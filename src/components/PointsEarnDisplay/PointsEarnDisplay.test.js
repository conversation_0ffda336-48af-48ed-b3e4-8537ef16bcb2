import React from 'react';
import { mountUtils } from 'test-utils';
import PointsEarnDisplay from './PointsEarnDisplay';

describe('<PointsEarnDisplay />', () => {
  const render = (props) => mountUtils(<PointsEarnDisplay {...props} />);

  describe('with no "base"', () => {
    it('renders only the total', () => {
      const { wrapper } = render({ total: 1000 });
      expect(wrapper.text()).toEqual('1,000');
    });
  });

  describe('with "base" of 0"', () => {
    it('renders only the total', () => {
      const { wrapper } = render({ total: 0, base: 0 });
      expect(wrapper.text()).toEqual('0');
    });
  });

  describe('with "base" exists and equals total', () => {
    it('renders only the total', () => {
      const { wrapper } = render({ total: 1000, base: 1000 });
      expect(wrapper.text()).toEqual('1,000');
    });
  });

  describe('with "base" exists and base not equals total', () => {
    it('renders only the total', () => {
      const { find } = render({ total: 1000, base: 800 });
      expect(find('Points').first()).toHaveText('800');
      expect(find('Points').last()).toHaveText('1,000');
    });
  });
});
