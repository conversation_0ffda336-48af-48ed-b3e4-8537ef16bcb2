import React from 'react';
import { NakedButton } from '@qga/roo-ui/components';
import { mountUtils } from 'test-utils';
import PointsClubModal from './PointsClubModal';
import { useDataLayer } from 'hooks/useDataLayer';
import { openPointsClubModal } from 'store/ui/uiActions';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');
const emitInteractionEvent = jest.fn();

const render = (props) =>
  mountUtils(
    <PointsClubModal {...props}>
      <div data-testid="modal-cta">Click me</div>
    </PointsClubModal>,
    { decorators: { theme: true, store: true } },
  );

const renderModal = (props) => {
  const { wrapper, findByTestId } = render(props);
  findByTestId('points-club-modal-button').first().simulate('click');
  wrapper.update();
  return wrapper.find('Modal');
};

describe('<PointsClubModal />', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    useDataLayer.mockReturnValue({ emitInteractionEvent });
  });

  it('renders the CTA for the modal', () => {
    const { findByTestId } = render();
    const cta = findByTestId('modal-cta');
    expect(cta).toExist();
    expect(cta.text()).toEqual('Click me');
  });

  it('renders the modal itself', () => {
    const modal = renderModal();
    const copy = modal.find('[data-testid="points-club-modal-copy"]');
    expect(copy).toExist();
    expect(copy.first().text()).toEqual(
      'We created Points Club to recognise members who earn a high amount of points on the ground. This could be from spending on your credit card, grocery shopping or your energy bills.',
    );
    const pointsClubIcons = modal.find('PointsClubImage');
    expect(pointsClubIcons).toHaveLength(2);
  });

  it('renders the Learn More link', () => {
    const modal = renderModal({ displayLearnMore: true });
    const learnMore = modal.find('[data-testid="points-club-modal-learn-more"]');
    expect(learnMore).toExist();
  });

  it('does NOT render the Learn More link', () => {
    const modal = renderModal();
    const learnMore = modal.find('[data-testid="points-club-modal-learn-more"]');
    expect(learnMore).not.toExist();
  });

  it('dispatches onOpenPointsClubModal when the modal is opened', () => {
    const { decorators, find } = render();

    const openButton = find(NakedButton);
    openButton.simulate('click');

    expect(decorators.store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: openPointsClubModal.type,
      }),
    );
  });

  it('emits an event to the data layer when the modal is dismissed', () => {
    const modal = renderModal();

    const closeButton = modal.find('[data-testid="close"]');
    closeButton.first().simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Points Club Pop Up',
      value: 'Dismiss Selected',
    });
  });

  it('emits an event to the data layer when the learn more link is clicked', () => {
    const modal = renderModal({ displayLearnMore: true });

    const learnMore = modal.find('[data-testid="points-club-modal-learn-more"]');
    learnMore.first().simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Points Club Pop Up',
      value: 'Learn More Link Selected',
    });
  });
});
