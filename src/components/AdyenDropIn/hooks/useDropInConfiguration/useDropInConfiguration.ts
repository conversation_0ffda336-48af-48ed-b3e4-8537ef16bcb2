import { useState, useMemo } from 'react';
import { Card, CardConfigSuccessData, DropinConfiguration } from 'adyen-web-v6';
import getMaskedCardNumber from './utils/getMaskedCardNumber';
import { QepgVault } from 'lib/clients/getPaymentMethods';
import { PAYMENT_TYPE_CARD, FIELD_TYPE_ENCRYPTED_CARD_NUMBER } from 'config';

interface CardBinValueData {
  type: string;
  binValue: string;
  encryptedBin?: string;
}

interface CardFieldValidData {
  fieldType: string;
  valid: boolean;
  type: string;
  endDigits?: string;
  expiryDate?: string;
}

interface Config {
  qepgVault?: QepgVault;
  isStoredPaymentAvailable?: boolean;
}

interface Result {
  dropInConfiguration: DropinConfiguration;
  isDropInReady: boolean;
  isLoading: boolean;
  creditCardIdentifiers?: {
    bin: string;
    tokenizedCreditCard: string;
  };
}

const useDropInConfiguration = (config?: Config): Result => {
  const [isDropInReady, setIsDropInReady] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [bin, setBin] = useState<string | null>(null);
  const [endDigits, setEndDigits] = useState<string | null>(null);
  const tokenizedCreditCard = useMemo(() => getMaskedCardNumber(bin, endDigits), [bin, endDigits]);
  const creditCardIdentifiers =
    bin && tokenizedCreditCard
      ? {
          bin,
          tokenizedCreditCard,
        }
      : undefined;

  const dropInConfiguration = useMemo(
    () => ({
      paymentMethodComponents: [Card],
      openFirstPaymentMethod: true,
      disableFinalAnimation: true,
      paymentMethodsConfiguration: {
        card: {
          name: `Enter ${!!config?.qepgVault && config?.isStoredPaymentAvailable ? 'new' : 'your'} credit or debit card details`,
          hasHolderName: true,
          holderNameRequired: true,
          hideCVC: false,
          billingAddressRequired: false,
          enableStoreDetails: !!config?.qepgVault,
          onConfigSuccess: ({ iframesConfigured }: CardConfigSuccessData) => setIsLoading(!iframesConfigured),
          onBinValue: ({ type, binValue, encryptedBin }: CardBinValueData) => {
            if (type === PAYMENT_TYPE_CARD) {
              setBin(encryptedBin ? binValue : null);
            }
          },
          onFieldValid: ({ valid, type, fieldType, endDigits }: CardFieldValidData) => {
            if (type === PAYMENT_TYPE_CARD && fieldType === FIELD_TYPE_ENCRYPTED_CARD_NUMBER) {
              setEndDigits(valid && endDigits ? endDigits : null);
            }
          },
        },
        storedCard: {
          onConfigSuccess: ({ iframesConfigured }: CardConfigSuccessData) => setIsLoading(!iframesConfigured),
        },
      },
      onReady: () => setIsDropInReady(true),
    }),
    [config],
  );

  return {
    dropInConfiguration,
    isDropInReady,
    isLoading,
    creditCardIdentifiers,
  };
};

export default useDropInConfiguration;
