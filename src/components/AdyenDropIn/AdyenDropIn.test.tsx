import React, { ReactNode, createRef } from 'react';
import { mocked } from 'test-utils';
import { render, screen, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import AdyenDropIn, { AdyenDropInHandle } from '../AdyenDropIn';
// @ts-expect-error: this is a mock, not part of real module
import { mockDropinInstance } from 'adyen-web-v6';
import getMemberId from 'lib/getMemberId';

const memberId = '**********';
const qepgVault = {
  memberId: '**********',
  accountType: 'QFF',
};
const paymentMethods = [
  {
    type: 'scheme',
    name: 'Credit Card',
    brands: ['visa', 'mc', 'amex'],
  },
];
const encryptedCreditCard = {
  brand: 'visa',
  holderName: '<PERSON>',
  encryptedCardNumber: 'eyJhbGciOiJSU0EtT0...UifQ',
  encryptedExpiryMonth: 'eyJhbGciOiJSU0EtT1...UifQ',
  encryptedExpiryYear: 'eyJhbGciOiJSU0EtT2...UifQ',
  encryptedSecurityCode: 'eyJhbGciOiJSU0EtT3...UifQ',
};
const creditCardIdentifiers = {
  bin: '123456',
  tokenizedCreditCard: 'tokenized-card',
};
const creditCardPayload = {
  sessionId: null,
  expiryMonth: null,
  expiryYear: null,
  nameOnCard: null,
  apiVersion: null,
  merchantId: null,
  ...creditCardIdentifiers,
  cardType: encryptedCreditCard.brand,
  holderName: encryptedCreditCard.holderName,
  encryptedCardNumber: encryptedCreditCard.encryptedCardNumber,
  encryptedExpiryMonth: encryptedCreditCard.encryptedExpiryMonth,
  encryptedExpiryYear: encryptedCreditCard.encryptedExpiryYear,
  encryptedSecurityCode: encryptedCreditCard.encryptedSecurityCode,
};

jest.mock('adyen-web-v6');
jest.mock('lib/getMemberId');

const mockStore = configureStore([]);
const store = mockStore({});

const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => mockDispatch,
}));
jest.mock('components/Loader', () => {
  return ({ children }: { children: ReactNode }) => <>{children}</>;
});
jest.mock('./hooks/usePaymentMethods', () => () => ({
  fetch: () => {},
  isPaymentMethodsReady: true,
  paymentMethods,
  storedPaymentMethods: [],
}));
jest.mock('./hooks/useAdyenConfiguration', () => () => ({
  adyenConfiguration: {
    environment: 'test',
    clientKey: 'adyen-client-key',
    countryCode: 'AU',
    locale: 'en-US',
    showPayButton: false,
    paymentMethodsResponse: {
      paymentMethods,
    },
  },
}));
jest.mock('./hooks/useDropInConfiguration', () => () => ({
  dropInConfiguration: {
    openFirstPaymentMethod: true,
    disableFinalAnimation: true,
  },
  isDropInReady: false,
  isLoading: true,
  creditCardIdentifiers,
}));

const mockScrollIntoView = jest.fn();
beforeEach(() => {
  Element.prototype.scrollIntoView = mockScrollIntoView;

  mockDropinInstance.isValid = true;
  mockDropinInstance.data = {
    paymentMethod: {
      ...encryptedCreditCard,
    },
    storePaymentMethod: false,
  };
  mockDropinInstance.showValidation.mockClear();
  mockDropinInstance.remove.mockClear();
  mocked(getMemberId).mockReturnValue(memberId);
});

afterEach(() => {
  jest.clearAllMocks();
  mockDispatch.mockClear();
});

test('mounts the Dropin, renders adyen and calls validate functions', async () => {
  const ref = createRef<AdyenDropInHandle>();
  await act(async () => {
    render(
      <Provider store={store}>
        <AdyenDropIn ref={ref} qepgVault={qepgVault} />
      </Provider>,
    );
  });

  expect(screen.queryByTestId('adyen-drop-in-loader-skeleton')).not.toBeInTheDocument();
  expect(screen.getByTestId('adyen-drop-in')).toBeInTheDocument();

  act(() => {
    ref.current?.validate();
  });

  expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth', block: 'start' });
  expect(mockDropinInstance.showValidation).toHaveBeenCalled();
});

test('calls updatePayments when adyen form data is valid', async () => {
  mockDropinInstance.isValid = true;

  const ref = createRef<AdyenDropInHandle>();
  await act(async () => {
    render(
      <Provider store={store}>
        <AdyenDropIn ref={ref} qepgVault={qepgVault} />
      </Provider>,
    );
  });

  expect(ref.current?.isValid()).toBe(true);

  act(() => {
    ref.current?.updatePayments();
  });

  expect(mockDispatch).toHaveBeenCalled();
  expect(mockDispatch).toHaveBeenCalledWith(
    expect.objectContaining({
      type: 'checkout/UPDATE_PAYMENTS',
      payload: {
        cash: {
          creditCard: creditCardPayload,
        },
      },
    }),
  );
});

test('calls updatePayments with qepgVault data when memberId is valid, qepgVault data is available, feature-flag is on and store-details checkbox is selected', async () => {
  mockDropinInstance.isValid = true;
  mockDropinInstance.data = {
    paymentMethod: {
      ...encryptedCreditCard,
    },
    storePaymentMethod: true,
  };

  const ref = createRef<AdyenDropInHandle>();
  await act(async () => {
    render(
      <Provider store={store}>
        <AdyenDropIn ref={ref} qepgVault={qepgVault} />
      </Provider>,
    );
  });

  act(() => {
    ref.current?.updatePayments();
  });

  expect(mockDispatch).toHaveBeenCalledWith(
    expect.objectContaining({
      type: 'checkout/UPDATE_PAYMENTS',
      payload: {
        cash: {
          creditCard: {
            ...creditCardPayload,
            qepgVault,
          },
        },
      },
    }),
  );
});

test('calls updatePayments without qepgVault data when adyen form checkbox is checked but vault feature flag is false', async () => {
  mockDropinInstance.isValid = true;
  mockDropinInstance.data = {
    paymentMethod: {
      ...encryptedCreditCard,
    },
    storePaymentMethod: true,
  };

  const ref = createRef<AdyenDropInHandle>();
  await act(async () => {
    render(
      <Provider store={store}>
        <AdyenDropIn ref={ref} />
      </Provider>,
    );
  });

  act(() => {
    ref.current?.updatePayments();
  });

  expect(mockDispatch).toHaveBeenCalledWith(
    expect.objectContaining({
      type: 'checkout/UPDATE_PAYMENTS',
      payload: {
        cash: {
          creditCard: {
            ...creditCardPayload,
          },
        },
      },
    }),
  );
});

test('calls updatePayments without qepgVault data when adyen form checkbox is checked, vault feature flag is true but memberId is not available', async () => {
  mocked(getMemberId).mockReturnValue(undefined);
  mockDropinInstance.isValid = true;
  mockDropinInstance.data = {
    paymentMethod: {
      ...encryptedCreditCard,
    },
    storePaymentMethod: true,
  };

  const ref = createRef<AdyenDropInHandle>();
  await act(async () => {
    render(
      <Provider store={store}>
        <AdyenDropIn ref={ref} qepgVault={qepgVault} />
      </Provider>,
    );
  });

  act(() => {
    ref.current?.updatePayments();
  });

  expect(mockDispatch).toHaveBeenCalledWith(
    expect.objectContaining({
      type: 'checkout/UPDATE_PAYMENTS',
      payload: {
        cash: {
          creditCard: {
            ...creditCardPayload,
          },
        },
      },
    }),
  );
});

test('does not dispatch updatePayments if adyen form data is invalid', async () => {
  mockDropinInstance.isValid = false;

  const ref = createRef<AdyenDropInHandle>();
  await act(async () => {
    render(
      <Provider store={store}>
        <AdyenDropIn ref={ref} qepgVault={qepgVault} />
      </Provider>,
    );
  });

  expect(ref.current?.isValid()).toBe(false);

  act(() => {
    ref.current?.updatePayments();
  });

  expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth', block: 'start' });
  expect(mockDropinInstance.showValidation).toHaveBeenCalled();
  expect(mockDispatch).not.toHaveBeenCalled();
});
