import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { useDispatch } from 'react-redux';
import { AdyenCheckout, Dropin, IDropin } from 'adyen-web-v6';
import getMemberId from 'lib/getMemberId';
import { updatePayments } from 'store/checkout/checkoutActions';
import AdyenDropInLoaderSkeleton from './components/AdyenDropInLoaderSkeleton';
import Loader from 'components/Loader';
import { PaymentMethod, QepgVault, StoredPaymentMethod } from 'lib/clients/getPaymentMethods';
import useDropInConfiguration from './hooks/useDropInConfiguration';
import useAdyenConfiguration from './hooks/useAdyenConfiguration';

import 'adyen-web-v6/styles/adyen.css';

interface PaymentData {
  clientStateDataIndicator: boolean;
  origin: string;
  paymentMethod: {
    brand: string;
    cardType: string;
    holderName: string;
    encryptedCardNumber: string;
    encryptedExpiryMonth: string;
    encryptedExpiryYear: string;
    encryptedSecurityCode: string;
  };
  storePaymentMethod?: boolean;
}

interface AdyenDropIn extends IDropin {
  isValid: boolean;
  data: PaymentData;
  showValidation: () => void;
  remove: () => void;
}

export interface AdyenDropInHandle {
  isValid: () => boolean;
  validate: () => void;
  updatePayments: () => void;
}

interface AdyenDropInProps {
  paymentMethods?: PaymentMethod[];
  storedPaymentMethods?: StoredPaymentMethod[];
  qepgVault?: QepgVault;
}

const CREDIT_CARD_NULL_DATA = {
  sessionId: null,
  cardType: null,
  expiryMonth: null,
  expiryYear: null,
  nameOnCard: null,
  apiVersion: null,
  merchantId: null,
  bin: null,
  tokenizedCreditCard: null,
};

const AdyenDropIn = forwardRef<AdyenDropInHandle, AdyenDropInProps>(({ paymentMethods, storedPaymentMethods, qepgVault }, ref) => {
  const [adyenDropIn, setAdyenDropIn] = useState<AdyenDropIn>();

  const dispatch = useDispatch();
  const dropInRef = useRef<HTMLDivElement>(null);

  const { adyenConfiguration } = useAdyenConfiguration({
    paymentMethods,
    storedPaymentMethods,
  });
  const { dropInConfiguration, isDropInReady, isLoading, creditCardIdentifiers } = useDropInConfiguration({
    qepgVault,
    isStoredPaymentAvailable: !!storedPaymentMethods?.length,
  });

  const validate = () => {
    dropInRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    adyenDropIn?.showValidation();
  };

  useImperativeHandle(ref, () => ({
    isValid: () => adyenDropIn?.isValid || false,
    validate,
    updatePayments: () => {
      if (!adyenDropIn) {
        return;
      }

      if (!adyenDropIn.isValid) {
        validate();

        return;
      }

      const isValidMemberId = getMemberId() === qepgVault?.memberId;
      const isStoringDetailsAllowed = (isValidMemberId && qepgVault && adyenDropIn.data?.storePaymentMethod) || false;

      const creditCard = {
        ...CREDIT_CARD_NULL_DATA,
        ...creditCardIdentifiers,
        cardType: adyenDropIn.data.paymentMethod.brand,
        holderName: adyenDropIn.data.paymentMethod.holderName,
        encryptedCardNumber: adyenDropIn.data.paymentMethod.encryptedCardNumber,
        encryptedExpiryMonth: adyenDropIn.data.paymentMethod.encryptedExpiryMonth,
        encryptedExpiryYear: adyenDropIn.data.paymentMethod.encryptedExpiryYear,
        encryptedSecurityCode: adyenDropIn.data.paymentMethod.encryptedSecurityCode,
        ...(isStoringDetailsAllowed && { qepgVault }),
      };

      dispatch(
        updatePayments({
          cash: {
            creditCard,
          },
        }),
      );
    },
  }));

  useEffect(() => {
    if (!adyenConfiguration || !dropInConfiguration) {
      return;
    }

    const initializeDropIn = async (): Promise<void> => {
      if (isDropInReady || !dropInRef.current) {
        return;
      }

      const checkout = await AdyenCheckout(adyenConfiguration);
      const dropin = new Dropin(checkout, dropInConfiguration).mount(dropInRef.current);

      setAdyenDropIn(dropin);
    };

    initializeDropIn();
  }, [isDropInReady, adyenDropIn, adyenConfiguration, dropInConfiguration]);

  const LoaderComponent = () => <AdyenDropInLoaderSkeleton qepgVault={qepgVault} storedPaymentMethods={storedPaymentMethods} />;

  return (
    <Loader isLoading={isLoading} loaderComponent={LoaderComponent}>
      <div data-testid="adyen-drop-in" ref={dropInRef}></div>
    </Loader>
  );
});

export default AdyenDropIn;
