import React, { useState } from 'react';
import { Provider, useDispatch } from 'react-redux';
import { mocked } from 'test-utils';
import { act, renderHook } from '@testing-library/react-hooks';
import { emitPropertyPageStandardGa4Event } from 'store/propertyAvailability/propertyAvailabilityActions';
import { getAvailableRoomTypeCount, getGa4Pricing } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getGa4AvailableProperty, getPropertyLocation } from 'store/property/propertySelectors';
import { getSearchQuery } from 'store/search/searchSelectors';
import usePropertyPageStandardGa4Event from './usePropertyPageStandardGa4Event';
import { useRouter } from 'next/router';
import { getHasPageViewFired } from 'store/pageViewEvent/pageViewEventSelectors';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

jest.mock('store/search/searchSelectors');
jest.mock('store/pageViewEvent/pageViewEventSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/pageViewEvent/pageViewEventSelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn(),
}));
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));
jest.mock('lib/analytics/eventsMap/helpers/GetShowCtaMessage', () => ({
  GetShowCtaMessage: jest.fn(),
}));
jest.mock('hooks/optimizely/usePriceStrikethrough', () => ({
  __esModule: true,
  default: jest.fn(),
}));

const dispatch = jest.fn();

const mockRouter = {
  query: { ss_action: 'not-rebook' },
};

const property = {
  id: '124',
  name: 'Hotel',
  category: 'hotels',
  rating: 4,
  imageCount: 10,
  facilities: 'kettle, wifi, free parking',
  internationalOrDomestic: 'domestic',
};

const offerPricing = {
  averagePrice: 410,
  averagePriceBeforeDiscount: 510,
  averagePointsEarned: 1000,
  firstOffer: {
    allocationsAvailable: 2,
    charges: {
      strikethrough: {
        price: {
          amount: '600',
          currency: 'AUD',
        },
      },
    },
  },
  offerCount: 3,
  promotions: ['Exclusive Luxury Offer', 'Bonus Points'],
};

const regionName = 'Melbourne, VIC, Australia';

const query = { location: regionName, payWith: 'cash', adults: 2, children: 1, infants: 1, checkIn: '2024-05-18', checkOut: '2024-05-19' };

const payload = {
  results: { property: property },
  availableRooms: 3,
  availableOffers: 3,
  payWith: 'cash',
  currency: 'AUD',
  averagePrice: 410,
  averagePriceBeforeDiscount: 510,
  averagePointsEarned: 1000,
  averagePricePoints: 0,
  checkIn: '2024-05-18',
  checkOut: '2024-05-19',
  ctaMessage: '',
  ctaMessageCategory: '',
  adults: 2,
  children: 1,
  infants: 1,
  rebook: false,
  recommendedProperty: false,
  luxuryOffer: true,
  location: 'Melbourne, VIC, Australia',
  strikethrough: null,
};

const store = {
  dispatch: jest.fn(),
  getState: jest.fn().mockReturnValue({}),
  subscribe: jest.fn(),
};

let subject;

const wrapper = ({ children }) => <Provider store={store}>{children}</Provider>;
const render = () => renderHook(() => usePropertyPageStandardGa4Event(), { wrapper });

beforeEach(() => {
  jest.clearAllMocks();
  getSearchQuery.mockReturnValue(query);
  getGa4AvailableProperty.mockReturnValue(property);
  getGa4Pricing.mockReturnValue(offerPricing);
  getAvailableRoomTypeCount.mockReturnValue(3);
  getPropertyLocation.mockReturnValue(regionName);
  useDispatch.mockReturnValue(dispatch);
  useRouter.mockReturnValue(mockRouter);
  useState.mockReturnValue([false, jest.fn()]);
  getHasPageViewFired.mockReturnValue(true);
  mocked(GetShowCtaMessage).mockReturnValue(false);
  mocked(usePriceStrikethrough).mockReturnValue({ isPriceStrikethrough: false });
});

describe('usePropertyStandardOfferPageGa4Event', () => {
  describe('when getGa4Pricing has not resolved', () => {
    beforeEach(() => {
      getGa4Pricing.mockReturnValue([{ averagePrice: undefined }]);
    });
    it('does not dispatch emitPropertyPageStandardGa4Event', () => {
      render();
      expect(dispatch).not.toHaveBeenCalledWith(emitPropertyPageStandardGa4Event(payload));
    });
  });

  describe('when getGa4Pricing has resolved', () => {
    describe('when showCtaMessage is false', () => {
      beforeEach(() => {
        mocked(GetShowCtaMessage).mockReturnValue(false);
        subject = render();
      });

      it('dispatches emitPropertyPageStandardGa4Event with the correct payload', () => {
        const { standardViewItemEvent } = subject.result.current;
        act(() => standardViewItemEvent());
        expect(dispatch).toHaveBeenCalledWith(emitPropertyPageStandardGa4Event(payload));
      });
    });

    describe('when showCtaMessage is true', () => {
      beforeEach(() => {
        mocked(GetShowCtaMessage).mockReturnValue(true);
        subject = render();
      });

      it('dispatches emitPropertyPageStandardGa4Event with the correct payload', () => {
        const { standardViewItemEvent } = subject.result.current;
        act(() => standardViewItemEvent());
        expect(dispatch).toHaveBeenCalledWith(
          emitPropertyPageStandardGa4Event({ ...payload, ctaMessage: 'We only have 2 rooms left', ctaMessageCategory: 'available rooms' }),
        );
      });
    });

    describe('when isPriceStrikethrough is true', () => {
      beforeEach(() => {
        mocked(usePriceStrikethrough).mockReturnValue({ isPriceStrikethrough: true });
        subject = render();
      });

      it('dispatches emitPropertyPageStandardGa4Event with strikethrough data', () => {
        const { standardViewItemEvent } = subject.result.current;
        act(() => standardViewItemEvent());
        expect(dispatch).toHaveBeenCalledWith(
          emitPropertyPageStandardGa4Event({
            ...payload,
            strikethrough: offerPricing.firstOffer.charges.strikethrough,
          }),
        );
      });
    });
  });
});
