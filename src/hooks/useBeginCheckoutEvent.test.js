import { renderHook } from '@testing-library/react-hooks';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { beginCheckout } from 'store/checkout/checkoutActions';
import { fetchPointsBurnTiers } from 'store/pointsBurnTiers/pointsBurnActions';
import { getRoomAvailabilityMessageForOffer } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import useBeginCheckoutEvent from './useBeginCheckoutEvent';
import usePriceStrikethrough from './optimizely/usePriceStrikethrough';

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('store/checkout/checkoutActions', () => ({
  beginCheckout: jest.fn(),
}));
jest.mock('store/pointsBurnTiers/pointsBurnActions', () => ({
  fetchPointsBurnTiers: jest.fn(),
}));
jest.mock('components/RoomsAvailabilityMessage/getRoomAvailabilityMessage', () => ({
  getRoomAvailabilityMessageForOffer: jest.fn(),
}));
jest.mock('lib/analytics/eventsMap/helpers/GetShowCtaMessage', () => ({
  GetShowCtaMessage: jest.fn(),
}));

jest.mock('./optimizely/usePriceStrikethrough', () => ({
  __esModule: true,
  default: jest.fn(),
}));

const SELECTORS = {
  GET_QUOTE: 'getQuote',
  GET_PAYMENTS: 'getPayments',
  GET_POINTS_CONVERSION: 'getPointsConversion',
};

describe('useBeginCheckoutEvent', () => {
  const mockDispatch = jest.fn();
  let mockUseSelectorImpl;

  const defaultMockQuote = { id: 'defaultQuote', offer: { allocationsAvailable: 10 } };
  const defaultMockPayments = { method: 'defaultCard' };
  const defaultMockPointsConversion = { levels: [{ id: 'defaultLevel' }] };
  const defaultRouterQuery = {};
  const defaultShowCtaMessage = false;
  const defaultRoomAvailabilityMessage = '';
  const defaultIsPriceStrikethrough = false;

  const setupAndRenderHook = (options = {}) => {
    const {
      quote = defaultMockQuote,
      payments = defaultMockPayments,
      pointsConversion = defaultMockPointsConversion,
      routerQuery = defaultRouterQuery,
      showCtaMessage = defaultShowCtaMessage,
      roomAvailabilityMessage = defaultRoomAvailabilityMessage,
      isPriceStrikethrough = defaultIsPriceStrikethrough,
    } = options;

    mockUseSelectorImpl = (selector) => {
      const selectorName = typeof selector === 'function' ? selector.name : selector;

      if (selectorName === SELECTORS.GET_QUOTE) return quote;
      if (selectorName === SELECTORS.GET_PAYMENTS) return payments;
      if (selectorName === SELECTORS.GET_POINTS_CONVERSION) return pointsConversion;
      return undefined;
    };

    useSelector.mockImplementation(mockUseSelectorImpl);
    useRouter.mockReturnValue({ query: routerQuery });
    GetShowCtaMessage.mockReturnValue(showCtaMessage);
    getRoomAvailabilityMessageForOffer.mockReturnValue(roomAvailabilityMessage);
    usePriceStrikethrough.mockReturnValue({ isPriceStrikethrough });

    return renderHook(() => useBeginCheckoutEvent());
  };

  beforeEach(() => {
    mockDispatch.mockClear();
    useDispatch.mockReturnValue(mockDispatch);

    beginCheckout.mockClear();
    fetchPointsBurnTiers.mockClear();
    getRoomAvailabilityMessageForOffer.mockClear();
    GetShowCtaMessage.mockClear();
    usePriceStrikethrough.mockClear();

    useSelector.mockImplementation(jest.fn());
    useRouter.mockReturnValue({ query: {} });
  });

  it('should dispatch fetchPointsBurnTiers if hasLevels is initially false', () => {
    setupAndRenderHook({
      pointsConversion: { levels: [] },
      quote: null,
      payments: null,
    });

    expect(mockDispatch).toHaveBeenCalledWith(fetchPointsBurnTiers());
    expect(beginCheckout).not.toHaveBeenCalled();
  });

  it('should dispatch beginCheckout when conditions are met and event has not fired', () => {
    const customQuote = { id: 'quote123', offer: { allocationsAvailable: 5 } };
    const customPayments = { method: 'card' };
    const customPointsConversion = { levels: [{ id: 'level1' }] };
    const availableRoomsMessage = 'We have 5 rooms left at this price';

    setupAndRenderHook({
      quote: customQuote,
      payments: customPayments,
      pointsConversion: customPointsConversion,
      routerQuery: { ss_action: '' },
      showCtaMessage: true,
      roomAvailabilityMessage: availableRoomsMessage,
    });

    expect(fetchPointsBurnTiers).not.toHaveBeenCalled();
    expect(beginCheckout).toHaveBeenCalledTimes(1);
    expect(beginCheckout).toHaveBeenCalledWith({
      quote: customQuote,
      payments: customPayments,
      pointsConversion: customPointsConversion,
      isRebooked: false,
      ctaMessage: availableRoomsMessage,
      ctaMessageCategory: 'available rooms',
    });
  });

  it('should not dispatch beginCheckout if it has already fired', () => {
    const { rerender } = setupAndRenderHook({
      pointsConversion: { levels: [{ id: 'level1' }] },
      quote: { id: 'someQuote', offer: { allocationsAvailable: 1 } },
    });

    beginCheckout.mockClear();
    mockDispatch.mockClear();

    rerender();

    expect(beginCheckout).not.toHaveBeenCalled();
  });

  it('should set isRebooked to true if router.query.ss_action is "rebook"', () => {
    setupAndRenderHook({
      routerQuery: { ss_action: 'rebook' },
      pointsConversion: { levels: [{ id: 'level1' }] },
      quote: { id: 'rebookQuote', offer: { allocationsAvailable: 1 } },
    });

    expect(beginCheckout).toHaveBeenCalledWith(
      expect.objectContaining({
        isRebooked: true,
      }),
    );
  });

  it('should set correct ctaMessage and ctaMessageCategory when allocations are available and GetShowCtaMessage is true', () => {
    const specificAllocations = 1;
    const availableRoomsMessage = 'Hurry, we only have 1 room left at this price!';
    const quoteWithAllocations = { id: 'ctaQuote', offer: { allocationsAvailable: specificAllocations } };

    setupAndRenderHook({
      quote: quoteWithAllocations,
      pointsConversion: { levels: [{ id: 'level1' }] },
      showCtaMessage: true,
      roomAvailabilityMessage: availableRoomsMessage,
    });

    expect(GetShowCtaMessage).toHaveBeenCalledWith(specificAllocations);
    expect(getRoomAvailabilityMessageForOffer).toHaveBeenCalledWith(specificAllocations);
    expect(beginCheckout).toHaveBeenCalledWith(
      expect.objectContaining({
        ctaMessage: availableRoomsMessage,
        ctaMessageCategory: 'available rooms',
      }),
    );
  });

  it('should set empty ctaMessage and ctaMessageCategory when GetShowCtaMessage returns false', () => {
    const quoteWithZeroAllocations = { id: 'noCtaQuote', offer: { allocationsAvailable: 0 } };
    setupAndRenderHook({
      quote: quoteWithZeroAllocations,
      pointsConversion: { levels: [{ id: 'level1' }] },
      showCtaMessage: false,
    });

    expect(GetShowCtaMessage).toHaveBeenCalledWith(0);
    expect(getRoomAvailabilityMessageForOffer).not.toHaveBeenCalled();
    expect(beginCheckout).toHaveBeenCalledWith(
      expect.objectContaining({
        ctaMessage: '',
        ctaMessageCategory: '',
      }),
    );
  });

  it('should not dispatch beginCheckout if quote is null', () => {
    setupAndRenderHook({
      quote: null,
      pointsConversion: { levels: [{ id: 'level1' }] },
    });

    expect(beginCheckout).not.toHaveBeenCalled();
  });

  it('should dispatch fetchPointsBurnTiers and not beginCheckout if hasLevels is false, even if quote exists', () => {
    setupAndRenderHook({
      pointsConversion: { levels: [] },
      quote: { id: 'validQuote', offer: { allocationsAvailable: 5 } },
    });

    expect(mockDispatch).toHaveBeenCalledWith(fetchPointsBurnTiers());
    expect(beginCheckout).not.toHaveBeenCalled();
  });

  it('should remove strikethrough from quoteToDispatch if isPriceStrikethrough is false and strikethrough exists', () => {
    const quoteWithStrikethrough = {
      id: 'quoteWithST',
      offer: {
        allocationsAvailable: 1,
        charges: {
          strikethrough: { amount: 100, currency: 'USD' },
          total: { amount: 50, currency: 'USD' },
        },
      },
    };

    setupAndRenderHook({
      quote: quoteWithStrikethrough,
      isPriceStrikethrough: false,
    });

    expect(beginCheckout).toHaveBeenCalledWith(
      expect.objectContaining({
        quote: expect.objectContaining({
          offer: expect.objectContaining({
            charges: expect.not.objectContaining({
              strikethrough: expect.any(Object),
            }),
          }),
        }),
      }),
    );
  });

  it('should keep strikethrough in quoteToDispatch if isPriceStrikethrough is true and strikethrough exists', () => {
    const quoteWithStrikethrough = {
      id: 'quoteWithST',
      offer: {
        allocationsAvailable: 1,
        charges: {
          strikethrough: { amount: 100, currency: 'USD' },
          total: { amount: 50, currency: 'USD' },
        },
      },
    };

    setupAndRenderHook({
      quote: quoteWithStrikethrough,
      isPriceStrikethrough: true,
    });

    expect(beginCheckout).toHaveBeenCalledWith(
      expect.objectContaining({
        quote: expect.objectContaining({
          offer: expect.objectContaining({
            charges: expect.objectContaining({
              strikethrough: { amount: 100, currency: 'USD' },
            }),
          }),
        }),
      }),
    );
  });
});
