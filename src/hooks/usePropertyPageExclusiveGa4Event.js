import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { emitPropertyPageExclusiveGa4Event } from 'store/exclusiveOffer/exclusiveOfferActions';
import { getGa4AvailableProperty, getPropertyLocation } from 'store/property/propertySelectors';
import {
  getExclusiveOffersAvailableGa4Pricing,
  getExclusiveOfferInclusions,
  getExclusiveOfferSanityGa4Pricing,
} from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getSearchQuery } from 'store/search/searchSelectors';
import { getQueryRecommended } from 'store/router/routerSelectors';
import { useRouter } from 'next/router';
import { getHasPageViewFired } from 'store/pageViewEvent/pageViewEventSelectors';

const usePropertyPageExclusiveGa4Event = () => {
  const [fired, setFired] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const isRebooked = router.query.ss_action === 'rebook';
  const query = useSelector(getSearchQuery);
  const ga4Property = useSelector(getGa4AvailableProperty);
  const location = useSelector(getPropertyLocation);
  const recommendedProperty = useSelector(getQueryRecommended);
  const queryPricing = useSelector(getExclusiveOffersAvailableGa4Pricing);
  const sanityPricing = useSelector(getExclusiveOfferSanityGa4Pricing);
  const useSanityPricing = (!query?.checkIn && !query?.checkOut) || queryPricing?.offerCount === 0;
  const offerPricing = useSanityPricing ? sanityPricing : queryPricing;
  const exclusiveInclusions = useSelector(getExclusiveOfferInclusions);
  const parsedExclusiveInclusions = exclusiveInclusions.join(', ');
  const pageViewEventHasFired = useSelector(getHasPageViewFired);

  const exclusiveViewItemEvent = useCallback(() => {
    const exclusiveProperty = {
      id: ga4Property?.id,
      name: ga4Property?.name,
      category: ga4Property?.category,
      rating: ga4Property?.rating,
      imageCount: ga4Property?.imageCount,
      internationalOrDomestic: ga4Property?.internationalOrDomestic,
      facilities: parsedExclusiveInclusions,
    };

    if (offerPricing?.averagePrice && !fired && pageViewEventHasFired) {
      if (!query?.checkIn && !query?.checkOut) {
        dispatch(
          emitPropertyPageExclusiveGa4Event({
            results: { property: exclusiveProperty },
            availableRooms: offerPricing?.roomCount,
            availableOffers: offerPricing?.offerCount,
            payWith: query?.payWith,
            averagePrice: offerPricing?.averagePrice,
            averagePriceBeforeDiscount: offerPricing?.averagePriceBeforeDiscount,
            averagePricePoints: query?.payWith === 'points' ? offerPricing?.averagePrice : 0,
            averagePointsEarned: 0,
            ctaMessage: '',
            ctaMessageCategory: '',
            adults: 2,
            children: 0,
            infants: 0,
            currency: 'AUD',
            rebook: isRebooked,
            recommendedProperty: Boolean(recommendedProperty),
            luxuryOffer: true, // exclusive offers are luxury offers
            location: location,
            strikethrough: null,
          }),
        );
      } else if (query?.checkIn && query?.checkOut) {
        dispatch(
          emitPropertyPageExclusiveGa4Event({
            results: { property: exclusiveProperty },
            availableRooms: offerPricing?.roomCount,
            availableOffers: offerPricing?.offerCount,
            payWith: query?.payWith,
            averagePrice: offerPricing?.averagePrice,
            averagePriceBeforeDiscount: offerPricing?.averagePriceBeforeDiscount,
            averagePricePoints: query?.payWith === 'points' ? offerPricing?.averagePrice : 0,
            averagePointsEarned: query?.payWith === 'points' ? 0 : offerPricing?.averagePointsEarned,
            checkIn: query?.checkIn,
            checkOut: query?.checkOut,
            ctaMessage: '',
            ctaMessageCategory: '',
            adults: query?.adults,
            children: query?.children,
            infants: query?.infants,
            currency: 'AUD',
            rebook: isRebooked,
            recommendedProperty: Boolean(recommendedProperty),
            luxuryOffer: true, // exclusive offers are luxury offers
            location: location,
            strikethrough: null,
          }),
        );
      }
      setFired(true);
    }
  }, [
    ga4Property?.id,
    ga4Property?.name,
    ga4Property?.category,
    ga4Property?.rating,
    ga4Property?.imageCount,
    ga4Property?.internationalOrDomestic,
    parsedExclusiveInclusions,
    offerPricing?.averagePrice,
    offerPricing?.roomCount,
    offerPricing?.offerCount,
    offerPricing?.averagePriceBeforeDiscount,
    offerPricing?.averagePointsEarned,
    fired,
    pageViewEventHasFired,
    query?.checkIn,
    query?.checkOut,
    query?.payWith,
    query?.adults,
    query?.children,
    query?.infants,
    dispatch,
    isRebooked,
    recommendedProperty,
    location,
  ]);

  return { exclusiveViewItemEvent };
};

export default usePropertyPageExclusiveGa4Event;
