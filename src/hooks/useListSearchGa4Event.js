import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getSearchQuery, getSearchGa4Results, getSearchType } from 'store/search/searchSelectors';
import { emitSearchResultsEvent } from 'store/search/searchActions';
import { isEmpty, reduce } from 'lodash';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';
import { getRoomAvailabilityMessageStandard } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePriceStrikethrough from './optimizely/usePriceStrikethrough';

export default function useListSearchGa4Event() {
  const [fired, setFired] = useState(false);
  const [results, setResults] = useState({});
  const { showMessage, max_rooms_cutoff } = useAvailableRoomsMessage();
  const availableRoomsMaxCutoff = max_rooms_cutoff ?? 5;

  const dispatch = useDispatch();
  const query = useSelector(getSearchQuery);
  const type = useSelector(getSearchType);
  const searchResults = useSelector(getSearchGa4Results);
  const pointsConversion = useSelector(getPointsConversion);

  const propertyList = reduce(
    searchResults,
    (accum, result) => {
      return [...accum, result?.property?.name];
    },
    [],
  );

  const cachedPropertyList = reduce(
    results,
    (accum, result) => {
      return [...accum, result?.property?.name];
    },
    [],
  );

  const parsedPropertyList = propertyList.toString();
  const parsedCachedPropertyList = cachedPropertyList.toString();

  const { isPriceStrikethrough } = usePriceStrikethrough();

  const searchResultsWithCtaMessage = searchResults.map((result) => {
    const offerAllocationsAvailable = result?.offer?.allocationsAvailable;
    const hasMessage = showMessage && offerAllocationsAvailable > 0 && offerAllocationsAvailable <= availableRoomsMaxCutoff;

    let updatedOffer = JSON.parse(JSON.stringify(result.offer));
    if (!isPriceStrikethrough && updatedOffer?.charges?.strikethrough) {
      delete updatedOffer.charges.strikethrough;
    }

    if (hasMessage) {
      return {
        ...result,
        offer: updatedOffer,
        property: {
          ...result.property,
          ctaMessageCategory: 'available rooms',
          ctaMessage: getRoomAvailabilityMessageStandard(offerAllocationsAvailable),
        },
      };
    } else {
      return {
        ...result,
        offer: updatedOffer,
      };
    }
  });

  useEffect(() => {
    const hasLevels = !!pointsConversion?.levels?.length;
    if (hasLevels) {
      if (
        (!isEmpty(searchResults) && !fired && parsedCachedPropertyList !== parsedPropertyList) ||
        results[0]?.offer?.charges?.total?.amount !== searchResults[0]?.offer?.charges?.total?.amount ||
        parsedCachedPropertyList !== parsedPropertyList
      ) {
        dispatch(
          emitSearchResultsEvent({
            results: searchResultsWithCtaMessage,
            query: query,
            type: type,
            listName: 'Hotels Search Page',
            currency: 'AUD',
            category: 'qantas',
            pointsConversion: pointsConversion,
          }),
        );

        setFired(true);
        setResults(searchResults);
      }
    }
  }, [
    dispatch,
    type,
    fired,
    results,
    query,
    searchResults,
    cachedPropertyList,
    parsedPropertyList,
    parsedCachedPropertyList,
    pointsConversion?.levels?.length,
    pointsConversion,
    searchResultsWithCtaMessage,
  ]);
}
