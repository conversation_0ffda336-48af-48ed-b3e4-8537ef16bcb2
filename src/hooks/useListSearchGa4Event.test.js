import React from 'react';
import { Provider, useDispatch } from 'react-redux';
import { renderHook } from '@testing-library/react-hooks';
import { getSearchQuery, getSearchGa4Results, getSearchType } from 'store/search/searchSelectors';
import { emitSearchResultsEvent } from 'store/search/searchActions';
import useListSearchGa4Event from './useListSearchGa4Event';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePriceStrikethrough from './optimizely/usePriceStrikethrough';
import { getRoomAvailabilityMessageStandard } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';

jest.mock('store/search/searchSelectors');
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');
jest.mock('./optimizely/usePriceStrikethrough');

const dispatch = jest.fn();

const query = {
  location: 'Melbourne, VIC, Australia',
  payWith: 'cash',
  checkIn: '2024-05-18',
  checkOut: '2024-05-19',
  adults: 2,
  children: 0,
  infants: 0,
};

const baseResults = [
  {
    offer: {
      allocationsAvailable: 2,
      charges: {
        total: {
          amount: '300.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
      },
    },
    roomType: {
      name: 'room name',
    },
    property: {
      id: '1234',
      name: 'hotel name',
      category: 'hotels',
      hasOffer: true,
    },
  },
  {
    offer: {
      allocationsAvailable: 10,
      charges: {
        total: {
          amount: '400.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
      },
    },
    roomType: {
      name: 'room name 2',
    },
    property: {
      id: '1235',
      name: 'hotel name 2',
      category: 'hotels',
      hasOffer: true,
    },
  },
];

const resultsWithStrikethrough = [
  {
    ...baseResults[0],
    offer: {
      ...baseResults[0].offer,
      allocationsAvailable: 3,
      charges: {
        ...baseResults[0].offer.charges,
        price: {
          amount: '350',
          currency: 'AUD',
        },
      },
    },
  },
  {
    ...baseResults[1],
    offer: {
      ...baseResults[1].offer,
      charges: {
        ...baseResults[1].offer.charges,
        strikethrough: {
          price: {
            amount: '450',
            currency: 'AUD',
          },
        },
      },
    },
  },
];

const pointsConversion = {
  levels: [
    { min: 0, max: 150, rate: 0.00824 },
    { min: 150, max: 400, rate: 0.00834 },
    { min: 400, max: 650, rate: 0.00848 },
    { min: 650, max: 900, rate: 0.00875 },
    { min: 900, max: null, rate: 0.00931 },
  ],
  name: 'VERSION11',
};

const payload = {
  type: 'list',
  listName: 'Hotels Search Page',
  category: 'qantas',
  currency: 'AUD',
  query: query,
  results: baseResults,
  pointsConversion: pointsConversion,
};

const store = {
  dispatch: jest.fn(),
  getState: jest.fn().mockReturnValue({}),
  subscribe: jest.fn(),
};

const wrapper = ({ children }) => <Provider store={store}>{children}</Provider>;
const render = () => renderHook(() => useListSearchGa4Event(), { wrapper });

beforeEach(() => {
  jest.clearAllMocks();
  getSearchGa4Results.mockReturnValue(baseResults);
  getSearchQuery.mockReturnValue(query);
  getSearchType.mockReturnValue('list');
  useDispatch.mockReturnValue(dispatch);
  getPointsConversion.mockReturnValue(pointsConversion);
  useAvailableRoomsMessage.mockReturnValue({
    isReady: true,
    showMessage: true,
    max_rooms_cutoff: 5,
  });
  usePriceStrikethrough.mockReturnValue({ isPriceStrikethrough: true });
});

describe('ListSearchGa4Events', () => {
  describe('when getSearchGa4Results has not resolved', () => {
    beforeEach(() => {
      getSearchGa4Results.mockReturnValue([]);
    });
    it('does not dispatch emitSearchResultsEvent', () => {
      render();
      expect(dispatch).not.toHaveBeenCalledWith(emitSearchResultsEvent([]));
    });
  });

  describe('when getSearchGa4Results has resolved', () => {
    it('dispatches emitSearchResultsEvent with the correct payload (no strikethrough in results if isPriceStrikethrough is true)', () => {
      render();
      const expectedPayload = {
        ...payload,
        results: baseResults.map((result) => {
          const offerAllocationsAvailable = result?.offer?.allocationsAvailable;
          const hasMessage =
            useAvailableRoomsMessage().showMessage &&
            offerAllocationsAvailable > 0 &&
            offerAllocationsAvailable <= useAvailableRoomsMessage().max_rooms_cutoff;

          if (hasMessage) {
            return {
              ...result,
              property: {
                ...result.property,
                ctaMessageCategory: 'available rooms',
                ctaMessage: getRoomAvailabilityMessageStandard(offerAllocationsAvailable),
              },
            };
          } else {
            return result;
          }
        }),
      };
      expect(dispatch).toHaveBeenCalledWith(emitSearchResultsEvent(expectedPayload));
    });

    it('dispatches emitSearchResultsEvent with strikethrough present when isPriceStrikethrough is true', () => {
      getSearchGa4Results.mockReturnValue(resultsWithStrikethrough);
      usePriceStrikethrough.mockReturnValue({ isPriceStrikethrough: true });
      render();

      const expectedResults = resultsWithStrikethrough.map((result) => {
        const offerAllocationsAvailable = result?.offer?.allocationsAvailable;
        const hasMessage =
          useAvailableRoomsMessage().showMessage &&
          offerAllocationsAvailable > 0 &&
          offerAllocationsAvailable <= useAvailableRoomsMessage().max_rooms_cutoff;

        if (hasMessage) {
          return {
            ...result,
            property: {
              ...result.property,
              ctaMessageCategory: 'available rooms',
              ctaMessage: getRoomAvailabilityMessageStandard(offerAllocationsAvailable),
            },
          };
        } else {
          return result;
        }
      });

      const expectedPayloadWithStrikethrough = {
        ...payload,
        results: expectedResults,
      };

      expect(dispatch).toHaveBeenCalledWith(emitSearchResultsEvent(expectedPayloadWithStrikethrough));
    });

    it('dispatches emitSearchResultsEvent with strikethrough removed when isPriceStrikethrough is false', () => {
      getSearchGa4Results.mockReturnValue(resultsWithStrikethrough);
      usePriceStrikethrough.mockReturnValue({ isPriceStrikethrough: false });
      render();

      const expectedResults = resultsWithStrikethrough.map((result) => {
        const clonedOffer = JSON.parse(JSON.stringify(result.offer));
        delete clonedOffer.charges.strikethrough;

        const offerAllocationsAvailable = result?.offer?.allocationsAvailable;
        const hasMessage =
          useAvailableRoomsMessage().showMessage &&
          offerAllocationsAvailable > 0 &&
          offerAllocationsAvailable <= useAvailableRoomsMessage().max_rooms_cutoff;

        if (hasMessage) {
          return {
            ...result,
            offer: clonedOffer,
            property: {
              ...result.property,
              ctaMessageCategory: 'available rooms',
              ctaMessage: getRoomAvailabilityMessageStandard(offerAllocationsAvailable),
            },
          };
        } else {
          return {
            ...result,
            offer: clonedOffer,
          };
        }
      });

      const expectedPayloadWithoutStrikethrough = {
        ...payload,
        results: expectedResults,
      };

      expect(dispatch).toHaveBeenCalledWith(emitSearchResultsEvent(expectedPayloadWithoutStrikethrough));
    });
  });
});
