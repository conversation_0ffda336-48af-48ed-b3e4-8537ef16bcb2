import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getPayments } from 'store/checkout/checkoutSelectors';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';
import { getQuote } from 'store/quote/quoteSelectors';
import { useRouter } from 'next/router';
import { beginCheckout } from 'store/checkout/checkoutActions';
import { fetchPointsBurnTiers } from 'store/pointsBurnTiers/pointsBurnActions';
import { getRoomAvailabilityMessageForOffer } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import usePriceStrikethrough from './optimizely/usePriceStrikethrough';

export default function useBeginCheckoutEvent() {
  const [fired, setFired] = useState(false);
  const dispatch = useDispatch();
  const quote = useSelector(getQuote);
  const payments = useSelector(getPayments);
  const pointsConversion = useSelector(getPointsConversion);
  const router = useRouter();
  const isRebooked = router.query.ss_action === 'rebook';
  const hasLevels = !!pointsConversion?.levels?.length;
  const allocationsAvailable = quote?.offer?.allocationsAvailable;

  const availableRoomsMessage = GetShowCtaMessage(allocationsAvailable) ? getRoomAvailabilityMessageForOffer(allocationsAvailable) : '';

  const ctaMessage = availableRoomsMessage;
  const ctaMessageCategory = availableRoomsMessage ? 'available rooms' : '';

  const { isPriceStrikethrough } = usePriceStrikethrough();

  useEffect(() => {
    if (!hasLevels) {
      dispatch(fetchPointsBurnTiers());
    }
  }, [dispatch, hasLevels]);

  useEffect(() => {
    if (!fired && hasLevels && quote) {
      let quoteToDispatch = JSON.parse(JSON.stringify(quote));
      if (!isPriceStrikethrough && quoteToDispatch?.offer?.charges?.strikethrough) {
        delete quoteToDispatch.offer.charges.strikethrough;
      }
      dispatch(
        beginCheckout({
          quote: quoteToDispatch,
          payments,
          pointsConversion,
          isRebooked,
          ctaMessage,
          ctaMessageCategory,
        }),
      );

      setFired(true);
    }
  }, [pointsConversion, fired, quote, payments, dispatch, isRebooked, hasLevels, ctaMessage, ctaMessageCategory, isPriceStrikethrough]);

  return null;
}
