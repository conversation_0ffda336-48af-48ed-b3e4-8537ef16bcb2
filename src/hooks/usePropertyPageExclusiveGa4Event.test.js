import React, { useState } from 'react';
import { Provider, useDispatch } from 'react-redux';
import { act, renderHook } from '@testing-library/react-hooks';
import { emitPropertyPageExclusiveGa4Event } from 'store/exclusiveOffer/exclusiveOfferActions';
import {
  getExclusiveOffersAvailableGa4Pricing,
  getExclusiveOfferInclusions,
  getExclusiveOfferSanityGa4Pricing,
} from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getGa4AvailableProperty, getPropertyLocation } from 'store/property/propertySelectors';
import { getSearchQuery } from 'store/search/searchSelectors';
import { useRouter } from 'next/router';
import usePropertyPageExclusiveGa4Event from './usePropertyPageExclusiveGa4Event';
import { getHasPageViewFired } from 'store/pageViewEvent/pageViewEventSelectors';

jest.mock('store/search/searchSelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/pageViewEvent/pageViewEventSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn(),
}));

const dispatch = jest.fn();

const mockRouter = {
  query: { ss_action: 'not-rebook' },
};

const exclusiveProperty = {
  id: '124',
  name: 'Hotel',
  category: 'hotels',
  rating: 4,
  imageCount: 10,
  facilities: 'kettle, wifi, free breakfast',
  internationalOrDomestic: 'domestic',
};

const offerPricing = { averagePrice: 410, averagePriceBeforeDiscount: 510, averagePointsEarned: 1000, offerCount: 3, roomCount: 2 };

const regionName = 'Melbourne, VIC, Australia';

const query = {
  location: regionName,
  payWith: 'cash',
  checkIn: '2024-05-18',
  checkOut: '2024-05-19',
  adults: 2,
  children: 1,
  infants: 1,
};

const payload = {
  results: { property: exclusiveProperty },
  availableRooms: 2,
  availableOffers: 3,
  payWith: 'cash',
  currency: 'AUD',
  averagePrice: 410,
  averagePriceBeforeDiscount: 510,
  averagePointsEarned: 1000,
  averagePricePoints: 0,
  checkIn: '2024-05-18',
  checkOut: '2024-05-19',
  ctaMessage: '',
  ctaMessageCategory: '',
  adults: 2,
  children: 1,
  infants: 1,
  rebook: false,
  recommendedProperty: false,
  luxuryOffer: true,
  location: regionName,
  strikethrough: null,
};

const store = {
  dispatch: jest.fn(),
  getState: jest.fn().mockReturnValue({}),
  subscribe: jest.fn(),
};

let subject;

const wrapper = ({ children }) => <Provider store={store}>{children}</Provider>;
const render = () => renderHook(() => usePropertyPageExclusiveGa4Event(), { wrapper });

beforeEach(() => {
  jest.clearAllMocks();
  getSearchQuery.mockReturnValue(query);
  getExclusiveOffersAvailableGa4Pricing.mockReturnValue(offerPricing);
  getExclusiveOfferSanityGa4Pricing.mockReturnValue(offerPricing);
  getGa4AvailableProperty.mockReturnValue(exclusiveProperty);
  getPropertyLocation.mockReturnValue(regionName);
  getExclusiveOfferInclusions.mockReturnValue(['kettle', 'wifi', 'free breakfast']);
  useDispatch.mockReturnValue(dispatch);
  useRouter.mockReturnValue(mockRouter);
  useState.mockReturnValue([false, jest.fn()]);
  getHasPageViewFired.mockReturnValue(true);
  subject = render();
});

describe('usePropertyPageExclusiveGa4Event', () => {
  describe('when getExclusiveOfferGa4Pricing has not resolved', () => {
    beforeEach(() => {
      getExclusiveOffersAvailableGa4Pricing.mockReturnValue([{ averagePrice: undefined }]);
    });
    it('does not dispatch emitPropertyPageExclusiveGa4Event', () => {
      const { exclusiveViewItemEvent } = subject.result.current;
      act(() => exclusiveViewItemEvent());
      expect(dispatch).not.toHaveBeenCalledWith(emitPropertyPageExclusiveGa4Event([]));
    });
  });

  describe('when getExclusiveOfferGa4Pricing has resolved', () => {
    describe('when query includes checkIn and checkOut', () => {
      it('dispatches emitPropertyPageExclusiveGa4Event with the correct payload', () => {
        const { exclusiveViewItemEvent } = subject.result.current;
        act(() => exclusiveViewItemEvent());
        expect(dispatch).toHaveBeenCalledWith(emitPropertyPageExclusiveGa4Event(payload));
      });
    });

    describe('when query does not include checkIn and checkOut', () => {
      beforeEach(() => {
        getSearchQuery.mockReturnValue({
          location: regionName,
          payWith: 'cash',
          checkIn: undefined,
          checkOut: undefined,
          adults: 2,
          children: 1,
          infants: 1,
        });
      });

      it('dispatches emitPropertyPageExclusiveGa4Event with the correct payload', () => {
        const payload = {
          results: { property: exclusiveProperty },
          availableRooms: 2,
          availableOffers: 3,
          payWith: 'cash',
          currency: 'AUD',
          averagePrice: 410,
          averagePriceBeforeDiscount: 510,
          averagePointsEarned: 0,
          averagePricePoints: 0,
          ctaMessage: '',
          ctaMessageCategory: '',
          adults: 2,
          children: 0,
          infants: 0,
          rebook: false,
          recommendedProperty: false,
          luxuryOffer: true,
          location: regionName,
          strikethrough: null,
        };

        const wrapper = ({ children }) => <Provider store={store}>{children}</Provider>;
        const render = () => renderHook(() => usePropertyPageExclusiveGa4Event(), { wrapper });
        subject = render();
        const { exclusiveViewItemEvent } = subject.result.current;
        act(() => exclusiveViewItemEvent());
        expect(dispatch).toHaveBeenCalledWith(emitPropertyPageExclusiveGa4Event(payload));
      });
    });
  });
});
