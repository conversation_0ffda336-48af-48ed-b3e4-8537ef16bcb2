import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { emitPropertyPageStandardGa4Event } from 'store/propertyAvailability/propertyAvailabilityActions';
import { getAvailableRoomTypeCount, getGa4Pricing } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getGa4AvailableProperty, getPropertyLocation } from 'store/property/propertySelectors';
import { getSearchQuery } from 'store/search/searchSelectors';
import { getQueryRecommended } from 'store/router/routerSelectors';
import { useRouter } from 'next/router';
import { includes } from 'lodash';
import { getHasPageViewFired } from 'store/pageViewEvent/pageViewEventSelectors';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import { getRoomAvailabilityMessageForOffer } from 'components/RoomsAvailabilityMessage/getRoomAvailabilityMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

const usePropertyPageStandardGa4Event = () => {
  const [fired, setFired] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const isRebooked = router.query.ss_action === 'rebook';
  const offerPricing = useSelector(getGa4Pricing);
  const { firstOffer } = offerPricing;
  const showCtaMessage = GetShowCtaMessage(firstOffer?.allocationsAvailable);
  const ctaMessage = showCtaMessage ? getRoomAvailabilityMessageForOffer(firstOffer?.allocationsAvailable) : '';
  const ga4Property = useSelector(getGa4AvailableProperty);
  const availableRooms = useSelector(getAvailableRoomTypeCount);
  const query = useSelector(getSearchQuery);
  const location = useSelector(getPropertyLocation);
  const recommendedProperty = useSelector(getQueryRecommended);
  const pageViewEventHasFired = useSelector(getHasPageViewFired);
  const { isPriceStrikethrough } = usePriceStrikethrough();

  const standardViewItemEvent = useCallback(() => {
    if (offerPricing?.averagePrice && !fired && pageViewEventHasFired) {
      const propertyPromotions = offerPricing?.promotions;
      const luxuryOffer = includes(propertyPromotions, 'Exclusive Luxury Offer');
      dispatch(
        emitPropertyPageStandardGa4Event({
          results: { property: ga4Property },
          availableRooms: availableRooms,
          availableOffers: offerPricing?.offerCount,
          payWith: query?.payWith,
          averagePrice: offerPricing?.averagePrice,
          averagePriceBeforeDiscount: offerPricing?.averagePriceBeforeDiscount,
          averagePricePoints: query?.payWith === 'points' ? offerPricing?.averagePrice : 0,
          averagePointsEarned: query?.payWith === 'points' ? 0 : offerPricing?.averagePointsEarned,
          checkIn: query?.checkIn,
          checkOut: query?.checkOut,
          ctaMessage: ctaMessage,
          ctaMessageCategory: ctaMessage ? 'available rooms' : '',
          adults: query?.adults,
          children: query?.children,
          infants: query?.infants,
          currency: 'AUD',
          rebook: isRebooked,
          recommendedProperty: Boolean(recommendedProperty),
          luxuryOffer: luxuryOffer,
          location: location,
          strikethrough: isPriceStrikethrough ? firstOffer?.charges?.strikethrough : null,
        }),
      );

      setFired(true);
    }
  }, [
    offerPricing?.averagePrice,
    offerPricing?.promotions,
    offerPricing?.offerCount,
    offerPricing?.averagePriceBeforeDiscount,
    offerPricing?.averagePointsEarned,
    fired,
    pageViewEventHasFired,
    dispatch,
    ga4Property,
    availableRooms,
    query?.payWith,
    query?.checkIn,
    query?.checkOut,
    query?.adults,
    query?.children,
    query?.infants,
    ctaMessage,
    isRebooked,
    recommendedProperty,
    location,
    isPriceStrikethrough,
    firstOffer,
  ]);

  return { standardViewItemEvent };
};

export default usePropertyPageStandardGa4Event;
