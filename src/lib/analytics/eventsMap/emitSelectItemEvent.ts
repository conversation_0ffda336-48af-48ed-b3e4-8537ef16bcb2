import { createSelectItemEvent, createHotelsSelectItemEventPayload } from '@qantasexperiences/analytics';
import { selectItem } from 'store/property/propertyActions';

const emitSelectItem = ({ payload }) => {
  // map results list does not show strikethrough pricing
  const offer = JSON.parse(JSON.stringify(payload.offer));
  if (payload.type === 'map' && offer?.charges?.strikethrough) {
    delete offer.charges.strikethrough;
  }

  const selectItemEvent = createSelectItemEvent(
    createHotelsSelectItemEventPayload({
      listName: payload.listName,
      location: payload.location,
      type: payload.type,
      category: payload.category,
      payWith: payload.payWith,
      availableProperties: payload.availableProperties,
      availableRooms: payload.availableRooms,
      query: payload.query,
      property: payload.property,
      offer,
      roomType: payload.roomType,
      pointsConversion: payload.pointsConversion,
      ctaMessage: payload.ctaMessage,
      ctaMessageCategory: payload.ctaMessageCategory,
    }),
  );

  return selectItemEvent;
};

// eslint-disable-next-line
// @ts-ignore
export default { [selectItem]: emitSelectItem };
