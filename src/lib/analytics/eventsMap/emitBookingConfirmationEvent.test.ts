import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { trackBooking } from 'store/booking/bookingActions';
import emitBookingConfirmationEvent from './emitBookingConfirmationEvent';
import { getPageFromState } from './helpers';
import { TRACKING_PREFIX } from 'config';
import { ActionType, ComponentType, EventType, CreateHotelsPurchaseEventPayloadReturn } from '@qantasexperiences/analytics';

jest.mock('store/booking/bookingSelectors');
jest.mock('./helpers');

let store;

declare global {
  interface Window {
    dataLayer?: unknown[];
  }
}
const booking = {
  id: 'a7ac819c-3868-44aa-a55b-b021d4605f20',
  bookingTotal: {
    creditCard: { total: '100' },
    points: { totalPoints: '0.00' },
    voucher: { total: '0.00' },
    creditNote: { total: '0.00' },
    qantasGroupCreditVoucher: { total: '0.00' },
  },
  value: {
    amount: '100',
    currency: 'AUD',
  },
  reservation: {
    checkIn: '2023-10-20',
    checkOut: '2023-10-24',
    adults: 2,
    children: 1,
    infants: 0,
    propertyBookingReference: 'HD2Q3DHPJY',
    property: {
      id: '7567',
      name: 'Bowral Hotel',
      category: 'hotels',
      rating: '4.0',
      address: { suburb: 'Bowral', state: 'NSW', country: 'Australia' },
    },
    roomType: {
      name: 'Single Room',
      roomTypeFacilities: ['Free Wi-fi', 'Free Breakfast'],
    },
    offer: {
      name: 'Parking Included',
      charges: {
        total: { amount: '100', currency: 'AUD' },
        payableAtBooking: {
          total: { amount: '100', currency: 'AUD' },
          tax: { amount: '10', currency: 'AUD' },
          taxDisplayable: { amount: '10', currency: 'AUD' },
        },
        payableAtProperty: {
          total: { amount: '0', currency: 'AUD' },
          tax: { amount: '0', currency: 'AUD' },
        },
      },
      promotion: {
        name: 'Luxury Exclusive Offer',
        promotionCode: 'luxeExclusiveOffer',
      },
      pointsEarned: { qffPoints: { total: 130 } },
      pointsTierInstance: {
        id: 'c42a9dae-a298-4b3c-99e6-17b2bd33f643',
        family: 'd5fc7d01-38cb-4b30-a322-d21002fe7b00',
        name: 'VERSION11',
        levels: [
          {
            min: 0,
            max: 150,
            rate: '0.00824',
          },
          {
            min: 150,
            max: 400,
            rate: '0.00834',
          },
          {
            min: 400,
            max: 650,
            rate: '0.00848',
          },
          {
            min: 650,
            max: 900,
            rate: '0.00875',
          },
          {
            min: 900,
            max: null,
            rate: '0.00931',
          },
        ],
      },
    },
  },
};
const quote = {
  offer: {
    name: 'Parking Included',
    charges: {
      total: {
        amount: '100',
        currency: 'AUD',
      },
      totalBeforeDiscount: {
        amount: '100',
        currency: 'AUD',
      },
      payableAtBooking: {
        discount: {
          amount: '0',
          currency: 'AUD',
        },
        tax: {
          amount: '0',
          currency: 'AUD',
        },
      },
      payableAtProperty: {
        total: {
          amount: '0.00',
          currency: 'AUD',
        },
      },
    },
    pointsEarned: {
      qffPoints: { total: 504 },
    },
    promotion: null,
    allocationsAvailable: '61',
  },
  property: {
    id: '160352',
    rating: 4,
    name: 'Comfort Inn & Suites Burwood',
    category: 'hotels',
    address: {
      state: 'New South Wales',
      country: 'Australia',
    },
  },
  roomType: {
    name: 'Single Room',
    roomTypeFacilities: [
      'Microwave',
      'Towels provided',
      'WiFi (surcharge)',
      'Coffee/tea maker',
      'Hair dryer',
      'Phone',
      'LCD TV',
      'Rollaway/extra beds (surcharge)',
      'Non-Smoking',
      'Bedsheets provided',
      'In-room safe',
      'Cribs/infant beds (surcharge)',
      'Cable TV service',
      'Free toiletries',
      'Daily housekeeping',
      'Iron/ironing board',
      'Bathtub or shower',
      'Air conditioning',
      'Minibar',
      'Free weekday newspaper',
      'Desk',
      'Mini-fridge',
      'Room service (limited hours)',
      'Shower only',
    ],
  },
  stay: {
    adults: 2,
    children: 0,
    infants: 0,
    checkIn: '2024-05-29',
    checkOut: '2024-05-30',
  },
};

const quoteWithFiveAllocations = { ...quote, offer: { ...quote.offer, allocationsAvailable: '5' } };
const quoteWithStrikethrough = {
  ...quote,
  offer: {
    ...quote.offer,
    charges: {
      ...quote.offer.charges,
      strikethrough: {
        price: {
          amount: '200',
          currency: 'AUD',
        },
      },
    },
  },
};

const middleware = createMiddleware(emitBookingConfirmationEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware]);
const createStore = (initialState) => mockStore(initialState);
interface MockPurchaseEvent extends CreateHotelsPurchaseEventPayloadReturn {
  event: EventType;
}

const getBaseExpectedDataLayer = (ctaMessage?: string | null, ctaMessageCategory?: string | null): MockPurchaseEvent => ({
  event: EventType.PURCHASE,
  event_data: {
    action: ActionType.PURCHASE,
    component_type: ComponentType.CHECKOUT,
    ...(ctaMessage && { cta_message: ctaMessage }),
    ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
  },
  ecommerce: {
    transaction_id: 'a7ac819c-3868-44aa-a55b-b021d4605f20',
    value: 100,
    currency: 'AUD',
    deposit_pay_percentage: 0,
    discount_value: 0,
    coupon: 'luxeExclusiveOffer',
    payment_type: 'credit card',
    shipping: 0,
    tax: 10,
    total_cash_used: 100,
    total_points_earned: 130,
    total_points_used: 0,
    total_points_used_percentage: 0,
    total_points_value: 12136,
    voucher_used: false,
    voucher_value: 0,
    items: [
      {
        item_id: '7567',
        item_name: 'Bowral Hotel',
        cash_used: 100,
        end_date: '2023-10-24',
        exclusive_offer: true,
        has_offer: true,
        includes: 'Free Wi-fi, Free Breakfast',
        international_or_domestic: 'Domestic',
        item_category: 'hotels',
        index: 0,
        item_offer: 'Parking Included',
        item_variant: 'Single Room',
        location: 'NSW, Australia',
        luxe: true,
        number_of_nights: 4,
        number_of_rooms: 1,
        offer_type: 'Luxury Exclusive Offer',
        original_price: 25,
        pay_in_points_percentage: 0,
        payment_type: 'credit card',
        points_earned: 130,
        points_used: 0,
        price: 25,
        rating: 4,
        rebook: true,
        start_date: '2023-10-20',
        strikethrough_price: false,
        travellers_adult: 2,
        travellers_children: 1,
        travellers_infant: 0,
        ...(ctaMessage && { cta_message: ctaMessage }),
        ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
      },
    ],
  },
});

describe('#emitBookingConfirmationEvent', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (getPageFromState as jest.Mock).mockReturnValue({ name: `${TRACKING_PREFIX} Booking Confirmation Page` });

    window.dataLayer = [];
    store = createStore({});
  });

  it('fires with the correct data', () => {
    store.dispatch(trackBooking({ booking, quote, isRebooked: true }));

    const expectedDataLayer = getBaseExpectedDataLayer();

    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });

  it('fires with the correct data when there is available rooms message', () => {
    store.dispatch(
      trackBooking({
        booking,
        quote: quoteWithFiveAllocations,
        isRebooked: true,
        ctaMessage: 'We have 5 rooms left at this price',
        ctaMessageCategory: 'available rooms',
      }),
    );
    const expectedDataLayer = getBaseExpectedDataLayer('We have 5 rooms left at this price', 'available rooms');
    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });

  it('fires with the correct data when there is strikethrough pricing', () => {
    store.dispatch(trackBooking({ booking, quote: quoteWithStrikethrough, isRebooked: true }));

    const expectedDataLayer = getBaseExpectedDataLayer();
    (expectedDataLayer.ecommerce.items[0] as CreateHotelsPurchaseEventPayloadReturn['ecommerce']['items'][0]).strikethrough_price = true;

    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });
});
