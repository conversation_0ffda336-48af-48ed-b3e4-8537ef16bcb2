import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { routerMiddleware } from 'connected-react-router';
import emitViewItemListEvent from './emitViewItemListEvent';
import { emitSearchResultsEvent } from 'store/search/searchActions';
import { emitDealsPageResults } from 'store/deal/dealActions';
import { emitRecommendedPropertiesResult } from 'store/recommendedProperty/recommendedPropertyActions';
import { emitPromoAreaGa4Results } from 'store/promoArea/promoAreaActions';

let store;

const defaultInitialState = { router: { action: 'push' } };

const middleware = createMiddleware(emitViewItemListEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware, routerMiddleware]);
const createStore = (initialState) => mockStore({ ...defaultInitialState, ...initialState });

const commonResults = [
  {
    offer: {
      charges: {
        total: {
          amount: '272.00',
          currency: 'AUD',
        },
      },
    },
    roomType: {
      name: 'room name',
    },
    property: {
      id: '1234',
      name: 'hotel name',
      category: 'hotels',
      hasOffer: true,
    },
  },
  {
    offer: {
      charges: {
        total: {
          amount: '255.00',
          currency: 'AUD',
        },
      },
    },
    roomType: {
      name: 'room name 2',
    },
    property: {
      id: '1235',
      name: 'hotel name 2',
      category: 'hotels',
      hasOffer: true,
    },
  },
];

const commonResultsWithStrikethrough = [
  {
    offer: {
      charges: {
        total: {
          amount: '272.00',
          currency: 'AUD',
        },
        strikethrough: {
          price: {
            amount: '300',
            currency: 'AUD',
          },
        },
      },
    },
    roomType: {
      name: 'room name',
    },
    property: {
      id: '1234',
      name: 'hotel name',
      category: 'hotels',
      hasOffer: true,
    },
  },
  {
    offer: {
      charges: {
        total: {
          amount: '255.00',
          currency: 'AUD',
        },
        strikethrough: {
          price: {
            amount: '280',
            currency: 'AUD',
          },
        },
      },
    },
    roomType: {
      name: 'room name 2',
    },
    property: {
      id: '1235',
      name: 'hotel name 2',
      category: 'hotels',
      hasOffer: true,
    },
  },
];

const commonPointsConversion = {
  levels: [
    { min: 0, max: 250, rate: 0.007 },
    { min: 250, max: 550, rate: 0.0074 },
    { min: 550, max: 1200, rate: 0.008 },
    { min: 1200, max: 2000, rate: 0.009 },
    { min: 2000, max: null, rate: 0.0095 },
  ],
  name: 'VERSION14',
};

const basePayload = {
  type: 'list',
  category: 'qantas',
  currency: 'AUD',
  query: {
    location: 'Melbourne, VIC, Australia',
    checkIn: '2024-05-17',
    checkOut: '2024-05-19',
    adults: 2,
    children: 0,
    infants: 0,
    payWith: 'cash',
  },
  results: commonResults,
  pointsConversion: commonPointsConversion,
};

const searchPayload = {
  ...basePayload,
  listName: 'Hotels Search Page',
  query: {
    ...basePayload.query,
    checkOut: '2024-05-18',
    children: 1,
    infants: 1,
    payWith: 'cash',
  },
  results: commonResults,
};

const searchPayloadWithStrikethrough = {
  ...basePayload,
  listName: 'Hotels Search Page',
  query: {
    ...basePayload.query,
    checkOut: '2024-05-18',
    children: 1,
    infants: 1,
    payWith: 'cash',
  },
  results: commonResultsWithStrikethrough,
};

const dealsPayload = {
  ...basePayload,
  listName: 'Deals Page',
  quantity: 2,
  query: {
    ...basePayload.query,
    payWith: 'cash and points',
  },
};

const recommendedPropertiesPayload = {
  ...basePayload,
  listName: 'Property Page Recommended Properties',
  quantity: 2,
};

const promoAreaPayload = {
  ...basePayload,
  listName: 'Promo Area Search Page',
  quantity: 2,
};

const latestDealsPayload = {
  ...basePayload,
  listName: 'Latest Deals Destination Page',
  quantity: 2,
};

const getExpectedDataLayer = ({ listName, query, quantity = 1, results = commonResults }) => {
  const item_list_id = listName.toLowerCase().replace(/\s/g, '_');
  const items = results.map((item, index) => {
    let points_value;
    if (item.property.id === '1234') {
      points_value = 38687;
    } else if (item.property.id === '1235') {
      points_value = 36390;
    }

    const hasStrikethrough = !!item.offer.charges.strikethrough;

    return {
      item_id: item.property.id,
      item_category: item.property.category,
      item_name: item.property.name,
      item_variant: item.roomType.name,
      index,
      quantity: quantity,
      price: parseFloat(item.offer.charges.total.amount),
      points_value: points_value,
      has_offer: item.property.hasOffer,
      strikethrough_price: hasStrikethrough,
    };
  });

  return [
    {
      event: 'view_item_list',
      event_data: {
        action: 'view',
        component_type: 'item_list',
        search_term: query.location,
        search_type: 'list',
        search_category: basePayload.category,
        search_payment_toggle: query.payWith,
        available_property_count: results.length,
        available_room_count: results.length,
        start_date: query.checkIn,
        end_date: query.checkOut,
        travellers_adult: query.adults,
        travellers_children: query.children || 0,
        travellers_infant: query.infants || 0,
      },
      ecommerce: {
        currency: basePayload.currency,
        item_list_id: item_list_id,
        item_list_name: listName,
        items: items,
      },
    },
  ];
};

beforeEach(() => {
  window.dataLayer = [];
  store = createStore({});
});

describe('emitViewItemListEvent', () => {
  test.each([
    {
      name: 'emitSearchResultsEvent (no strikethrough)',
      action: emitSearchResultsEvent,
      payload: searchPayload,
      expected: getExpectedDataLayer({
        listName: searchPayload.listName,
        query: searchPayload.query,
        results: searchPayload.results,
      }),
    },
    {
      name: 'emitSearchResultsEvent (with strikethrough)',
      action: emitSearchResultsEvent,
      payload: searchPayloadWithStrikethrough,
      expected: getExpectedDataLayer({
        listName: searchPayloadWithStrikethrough.listName,
        query: searchPayloadWithStrikethrough.query,
        results: searchPayloadWithStrikethrough.results,
      }),
    },
    {
      name: 'emitDealsPageResults',
      action: emitDealsPageResults,
      payload: dealsPayload,
      expected: getExpectedDataLayer({
        listName: dealsPayload.listName,
        query: dealsPayload.query,
        quantity: dealsPayload.quantity,
        results: dealsPayload.results,
      }),
    },
    {
      name: 'emitRecommendedPropertiesResult',
      action: emitRecommendedPropertiesResult,
      payload: recommendedPropertiesPayload,
      expected: getExpectedDataLayer({
        listName: recommendedPropertiesPayload.listName,
        query: recommendedPropertiesPayload.query,
        quantity: recommendedPropertiesPayload.quantity,
        results: recommendedPropertiesPayload.results,
      }),
    },
    {
      name: 'PromoAreaGa4Event',
      action: emitPromoAreaGa4Results,
      payload: promoAreaPayload,
      expected: getExpectedDataLayer({
        listName: promoAreaPayload.listName,
        query: promoAreaPayload.query,
        quantity: promoAreaPayload.quantity,
        results: promoAreaPayload.results,
      }),
    },
    {
      name: 'LatestDealsGa4Event',
      action: emitDealsPageResults,
      payload: latestDealsPayload,
      expected: getExpectedDataLayer({
        listName: latestDealsPayload.listName,
        query: latestDealsPayload.query,
        quantity: latestDealsPayload.quantity,
        results: latestDealsPayload.results,
      }),
    },
  ])('$name returns the correct dataLayer', ({ action, payload, expected }) => {
    store.dispatch(action(payload));
    expect(window.dataLayer).toStrictEqual(expected);
  });
});
