import { createViewItemEvent, createHotelsViewItemEventPayload } from '@qantasexperiences/analytics';
import { emitPropertyPageStandardGa4Event } from 'store/propertyAvailability/propertyAvailabilityActions';
import { emitPropertyPageExclusiveGa4Event } from 'store/exclusiveOffer/exclusiveOfferActions';

const emitViewItemEvent = ({ payload }) => {
  return createViewItemEvent(
    createHotelsViewItemEventPayload({
      averagePrice: payload?.averagePrice,
      averagePricePoints: payload?.averagePricePoints ?? 0,
      averagePriceBeforeDiscount: payload?.averagePriceBeforeDiscount,
      averagePointsEarned: payload?.averagePointsEarned ?? 0,
      currency: 'AUD',
      ctaMessage: payload?.ctaMessage,
      ctaMessageCategory: payload?.ctaMessageCategory,
      recommendedProperty: payload?.recommendedProperty,
      luxuryOffer: payload?.luxuryOffer,
      checkIn: payload?.checkIn,
      checkOut: payload?.checkOut,
      adults: payload?.adults,
      children: payload?.children,
      infants: payload?.infants,
      availableOffers: payload?.availableOffers,
      availableRooms: payload?.availableRooms,
      rebook: payload?.rebook,
      payWith: payload?.payWith,
      location: payload?.location,
      results: payload?.results,
      strikethrough: payload?.strikethrough,
    }),
  );
};

export default { [emitPropertyPageStandardGa4Event]: emitViewItemEvent, [emitPropertyPageExclusiveGa4Event]: emitViewItemEvent };
