import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { addToCart } from 'store/checkout/checkoutActions';
import emitAddToCartEvent from './emitAddToCartEvent';
import { getPageFromState } from './helpers';
import { TRACKING_PREFIX } from 'config';
import { ComponentType, CreateHotelsAddToCartEventPayloadReturn, EventType, PageType } from '@qantasexperiences/analytics';

jest.mock('store/booking/bookingSelectors');
jest.mock('./helpers');

let store;

declare global {
  interface Window {
    dataLayer?: unknown[];
  }
}

const payload = {
  property: {
    id: '160352',
    name: 'Comfort Inn & Suites Burwood',
    category: 'hotels',
    rating: 4,
    address: {
      state: 'New South Wales',
      country: 'Australia',
    },
  },
  roomType: {
    name: 'Single Room',
    roomTypeFacilities: [
      'Coffee/tea maker',
      'Hair dryer',
      'LCD TV',
      'Rollaway/extra beds (surcharge)',
      'Non-Smoking',
      'Cribs/infant beds (surcharge)',
      'Daily housekeeping',
      'Iron/ironing board',
      'Bathtub or shower',
      'Minibar',
      'Mini-fridge',
      'Microwave',
      'Towels provided',
      'WiFi (surcharge)',
      'Phone',
      'Bedsheets provided',
      'In-room safe',
      'Cable TV service',
      'Free toiletries',
      'Air conditioning',
      'Free weekday newspaper',
      'Desk',
      'Room service (limited hours)',
      'Shower only',
    ],
  },
  offer: {
    name: 'Parking Included',
    charges: {
      total: { amount: '168.98', currency: 'AUD' },
      totalBeforeDiscount: { amount: '168.98', currency: 'AUD' },
    },
    promotion: null,
    pointsEarned: {
      qffPoints: { total: 504 },
    },
  },
  query: {
    checkIn: '2024-05-29',
    checkOut: '2024-05-30',
    adults: 2,
    children: 0,
    infants: 0,
  },
  initialCash: null,
  pointsConversion: {
    levels: [
      { min: 0, max: 150, rate: 0.00824 },
      { min: 150, max: 400, rate: 0.00834 },
      { min: 400, max: 650, rate: 0.00848 },
      { min: 650, max: 900, rate: 0.00875 },
      { min: 900, max: null, rate: 0.00931 },
    ],
    name: 'VERSION11',
  },
  isRebooked: true,
};

const strikethroughPayload = {
  ...payload,
  offer: {
    ...payload.offer,
    charges: {
      ...payload.offer.charges,
      strikethrough: {
        price: {
          amount: '200.00',
          currency: 'AUD',
        },
      },
    },
  },
};

const availableRoomsPayload = {
  ...payload,
  ctaMessage: 'Hurry, we only have 1 room left!',
  ctaMessageCategory: 'available rooms',
};

const middleware = createMiddleware(emitAddToCartEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware]);
const createStore = (initialState) => mockStore(initialState);

interface MockAddToCartEvent extends CreateHotelsAddToCartEventPayloadReturn {
  event: EventType;
}

const getBaseExpectedDataLayer = (ctaMessage?: string | null, ctaMessageCategory?: string | null): MockAddToCartEvent => ({
  event: EventType.ADD_TO_CART,
  event_data: {
    component_type: ComponentType.CART,
    location: PageType.PROPERTY,
    ...(ctaMessage && { cta_message: ctaMessage }),
    ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
  },
  ecommerce: {
    value: 168.98,
    currency: 'AUD',
    items: [
      {
        item_id: '160352',
        item_name: 'Comfort Inn & Suites Burwood',
        item_category: 'hotels',
        item_variant: 'Single Room',
        item_offer: 'Parking Included',
        index: 0,
        quantity: 1,
        price: 168.98,
        original_price: 168.98,
        cash_used: 168.98,
        points_used: 0,
        points_value: 20480,
        has_offer: false,
        rating: 4,
        luxe: false,
        start_date: '2024-05-29',
        end_date: '2024-05-30',
        travellers_adult: 2,
        travellers_children: 0,
        travellers_infant: 0,
        number_of_nights: 1,
        number_of_rooms: 1,
        exclusive_offer: false,
        points_earned: 504,
        pay_in_points_percentage: 0,
        offer_type: undefined,
        rebook: true,
        includes:
          'Coffee/tea maker, Hair dryer, LCD TV, Rollaway/extra beds (surcharge), Non-Smoking, Cribs/infant beds (surcharge), Daily housekeeping, Iron/ironing board, Bathtub or shower, Minibar, Mini-fridge, Microwave, Towels provided, WiFi (surcharge), Phone, Bedsheets provided, In-room safe, Cable TV service, Free toiletries, Air conditioning, Free weekday newspaper, Desk, Room service (limited hours), Shower only',
        payment_type: 'cash',
        location: 'New South Wales, Australia',
        international_or_domestic: 'Domestic',
        strikethrough_price: false,
        ...(ctaMessage && { cta_message: ctaMessage }),
        ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
      },
    ],
  },
});

describe('#emitAddToCartEvent', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (getPageFromState as jest.Mock).mockReturnValue({ name: `${TRACKING_PREFIX} Booking Page` });

    window.dataLayer = [];
    store = createStore({});
  });

  it('fires with the correct data', () => {
    store.dispatch(addToCart(payload));

    const expectedDataLayer = getBaseExpectedDataLayer();

    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });

  it('fires with the correct data when strikethrough is available', () => {
    store.dispatch(addToCart(strikethroughPayload));

    const expectedDataLayer = getBaseExpectedDataLayer();
    (expectedDataLayer.ecommerce.items[0] as CreateHotelsAddToCartEventPayloadReturn['ecommerce']['items'][0]).strikethrough_price = true;

    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });

  it('fires with the correct data when there is the available rooms message', () => {
    store.dispatch(addToCart(availableRoomsPayload));

    const expectedDataLayer = getBaseExpectedDataLayer('Hurry, we only have 1 room left!', 'available rooms');
    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });
});
