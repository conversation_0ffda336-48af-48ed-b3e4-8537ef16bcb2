import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { routerMiddleware } from 'connected-react-router';
import emitViewItemEvent from './emitViewItemEvent';
import { emitPropertyPageExclusiveGa4Event } from 'store/exclusiveOffer/exclusiveOfferActions';
import { emitPropertyPageStandardGa4Event } from 'store/propertyAvailability/propertyAvailabilityActions';

let store;

const defaultInitialState = { router: { action: 'push' } };

const middleware = createMiddleware(emitViewItemEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware, routerMiddleware]);

const createTestStore = (initialState) => mockStore({ ...defaultInitialState, ...initialState });

const property = {
  id: '124',
  name: 'Hotel',
  category: 'hotels',
  rating: 4,
  imageCount: 10,
  facilities: 'kettle, wifi, free parking',
  internationalOrDomestic: 'domestic',
};

const payload = {
  currency: 'AUD',
  availableRooms: 2,
  availableOffers: 3,
  payWith: 'cash',
  averagePrice: 410,
  averagePriceBeforeDiscount: 510,
  averagePointsEarned: 1000,
  averagePricePoints: undefined,
  checkIn: '2024-05-18',
  checkOut: '2024-05-19',
  adults: 2,
  children: 1,
  infants: 1,
  rebook: false,
  recommendedProperty: true,
  luxuryOffer: true,
  location: 'Melbourne, VIC, Australia',
  results: { property: property },
};

const strikethroughPayload = {
  ...payload,
  strikethrough: {
    price: {
      amount: '600',
      currency: 'AUD',
    },
  },
};

const getExpectedDataLayer = (strikethroughPrice = false) => [
  {
    event: 'view_item',
    event_data: {
      action: 'view',
      component_type: 'item',
    },
    ecommerce: {
      value: 410,
      currency: 'AUD',
      items: [
        {
          item_id: '124',
          item_name: 'Hotel',
          item_category: 'hotels',
          item_variant: undefined,
          item_offer: undefined,
          quantity: 1,
          price: 410,
          original_price: 510,
          recommend: true,
          rating: 4,
          luxe: true,
          image_count: 10,
          start_date: '2024-05-18',
          end_date: '2024-05-19',
          travellers_adult: 2,
          travellers_children: 1,
          travellers_infant: 1,
          number_of_nights: 1,
          number_of_rooms: 1,
          available_offer_count: 3,
          available_room_count: 2,
          points_earned: 1000,
          pay_in_points_percentage: undefined,
          offer_type: undefined,
          rebook: false,
          includes: 'kettle, wifi, free parking',
          payment_type: 'cash',
          location: 'Melbourne, VIC, Australia',
          international_or_domestic: 'domestic',
          points_used: undefined,
          cash_used: undefined,
          points_value: 0,
          strikethrough_price: strikethroughPrice,
          index: 0,
        },
      ],
    },
  },
];

beforeEach(() => {
  window.dataLayer = [];
  store = createTestStore({});
});

describe('emitViewItemEvent', () => {
  describe('emitPropertyPageExclusiveGa4Event', () => {
    it('returns the correct dataLayer', () => {
      store.dispatch(emitPropertyPageExclusiveGa4Event(payload));
      expect(window.dataLayer).toStrictEqual(getExpectedDataLayer());
    });
  });

  describe('emitPropertyPageStandardGa4Event', () => {
    it('returns the correct dataLayer', () => {
      store.dispatch(emitPropertyPageStandardGa4Event(payload));
      expect(window.dataLayer).toStrictEqual(getExpectedDataLayer());
    });

    it('returns the correct dataLayer when strikethrough is provided', () => {
      store.dispatch(emitPropertyPageStandardGa4Event(strikethroughPayload));
      expect(window.dataLayer).toStrictEqual(getExpectedDataLayer(true));
    });
  });
});
