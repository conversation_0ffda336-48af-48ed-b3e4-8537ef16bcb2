import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { beginCheckout } from 'store/checkout/checkoutActions';
import emitBeginCheckoutEvent from './emitBeginCheckoutEvent';
import { getPageFromState } from './helpers';
import { TRACKING_PREFIX } from 'config';
import { ActionType, ComponentType, CreateHotelsBeginCheckoutEventPayloadReturn, EventType } from '@qantasexperiences/analytics';

jest.mock('store/booking/bookingSelectors');
jest.mock('./helpers');

let store;

declare global {
  interface Window {
    dataLayer?: unknown[];
  }
}

const payload = {
  quote: {
    offer: {
      name: 'Parking Included',
      charges: {
        payableAtProperty: { total: { amount: '0.00', currency: 'AUD' } },
        payableAtBooking: {
          discount: { amount: '0.00', currency: 'AUD' },
        },
        total: { amount: '168.98', currency: 'AUD' },
        totalBeforeDiscount: { amount: '168.98', currency: 'AUD' },
      },
      pointsEarned: {
        qffPoints: { total: 504 },
      },
      promotion: null,
    },
    property: {
      id: '160352',
      rating: 4,
      name: 'Comfort Inn & Suites Burwood',
      category: 'hotels',
      address: {
        state: 'New South Wales',
        country: 'Australia',
      },
    },
    roomType: {
      name: 'Single Room',
      roomTypeFacilities: [
        'Microwave',
        'Towels provided',
        'WiFi (surcharge)',
        'Coffee/tea maker',
        'Hair dryer',
        'Phone',
        'LCD TV',
        'Rollaway/extra beds (surcharge)',
        'Non-Smoking',
        'Bedsheets provided',
        'In-room safe',
        'Cribs/infant beds (surcharge)',
        'Cable TV service',
        'Free toiletries',
        'Daily housekeeping',
        'Iron/ironing board',
        'Bathtub or shower',
        'Air conditioning',
        'Minibar',
        'Free weekday newspaper',
        'Desk',
        'Mini-fridge',
        'Room service (limited hours)',
        'Shower only',
      ],
    },
    stay: {
      adults: 2,
      children: 0,
      infants: 0,
      checkIn: '2024-05-29',
      checkOut: '2024-05-30',
    },
  },
  payments: {
    voucher: { amount: null, code: null },
    points: { amount: '20480', amountInCash: '168.98' },
    cash: {
      payableNow: { amount: '0' },
    },
  },
  pointsConversion: {
    levels: [
      { min: 0, max: 150, rate: 0.00824 },
      { min: 150, max: 400, rate: 0.00834 },
      { min: 400, max: 650, rate: 0.00848 },
      { min: 650, max: 900, rate: 0.00875 },
      { min: 900, max: null, rate: 0.00931 },
    ],
    name: 'VERSION11',
  },
  isRebooked: false,
};

const middleware = createMiddleware(emitBeginCheckoutEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware]);
const createStore = (initialState) => mockStore(initialState);
interface MockBeginCheckoutEvent extends CreateHotelsBeginCheckoutEventPayloadReturn {
  event: EventType;
}

const getBaseExpectedDataLayer = (ctaMessage?: string | null, ctaMessageCategory?: string | null): MockBeginCheckoutEvent => ({
  event: EventType.BEGIN_CHECKOUT,
  event_data: {
    action: ActionType.START,
    component_type: ComponentType.CHECKOUT,
    ...(ctaMessage && { cta_message: ctaMessage }),
    ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
  },
  ecommerce: {
    value: 168.98,
    currency: 'AUD',
    items: [
      {
        ...(ctaMessage && { cta_message: ctaMessage }),
        ...(ctaMessageCategory && { cta_message_category: ctaMessageCategory }),
        item_id: '160352',
        item_name: 'Comfort Inn & Suites Burwood',
        item_category: 'hotels',
        item_variant: 'Single Room',
        index: 0,
        quantity: 1,
        price: 168.98,
        original_price: 168.98,
        cash_used: 0,
        points_used: 20480,
        points_value: 20480,
        has_offer: false,
        rating: 4,
        luxe: false,
        start_date: '2024-05-29',
        end_date: '2024-05-30',
        travellers_adult: 2,
        travellers_children: 0,
        travellers_infant: 0,
        number_of_nights: 1,
        offer_type: undefined,
        exclusive_offer: false,
        strikethrough_price: false,
        points_earned: 504,
        pay_in_points_percent: 100,
        rebook: false,
        includes:
          'Microwave, Towels provided, WiFi (surcharge), Coffee/tea maker, Hair dryer, Phone, LCD TV, Rollaway/extra beds (surcharge), Non-Smoking, Bedsheets provided, In-room safe, Cribs/infant beds (surcharge), Cable TV service, Free toiletries, Daily housekeeping, Iron/ironing board, Bathtub or shower, Air conditioning, Minibar, Free weekday newspaper, Desk, Mini-fridge, Room service (limited hours), Shower only',
        payment_type: 'points',
        location: 'New South Wales, Australia',
        international_or_domestic: 'Domestic',
      },
    ],
  },
});

const availableRoomsPayload = {
  ...payload,
  ctaMessage: 'Hurry, we only have 1 room left!',
  ctaMessageCategory: 'available rooms',
};

const strikethroughPayload = {
  ...payload,
  quote: {
    ...payload.quote,
    offer: {
      ...payload.quote.offer,
      charges: {
        ...payload.quote.offer.charges,
        strikethrough: {
          price: {
            amount: '200',
            currency: 'AUD',
          },
        },
      },
    },
  },
};

describe('#emitBeginCheckoutEvent', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (getPageFromState as jest.Mock).mockReturnValue({ name: `${TRACKING_PREFIX} Booking Page` });

    window.dataLayer = [];
    store = createStore({});
  });

  it('fires with the correct data', () => {
    store.dispatch(beginCheckout(payload));

    const expectedDataLayer = getBaseExpectedDataLayer();

    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });

  it('fires with the correct data when strikethrough is available', () => {
    store.dispatch(beginCheckout(strikethroughPayload));

    const expectedDataLayer = getBaseExpectedDataLayer();
    (expectedDataLayer.ecommerce.items[0] as CreateHotelsBeginCheckoutEventPayloadReturn['ecommerce']['items'][0]).strikethrough_price =
      true;

    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });

  it('fires with the correct data when there is the available rooms message', () => {
    store.dispatch(beginCheckout(availableRoomsPayload));

    const expectedDataLayer = getBaseExpectedDataLayer('Hurry, we only have 1 room left!', 'available rooms');
    expect(window.dataLayer).toStrictEqual([expectedDataLayer]);
  });
});
