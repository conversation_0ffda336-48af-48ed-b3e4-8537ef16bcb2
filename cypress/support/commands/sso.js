import { testId } from '../selectors';

Cypress.Commands.add('loginSteps', (memberId, lastName, pin) => {
  const { findByText } = cy;

  cy.get(testId('qff-login-tab')).first().should('be.visible').click();

  cy.get('input[name=memberId]').type(memberId);
  cy.get('input[name=lastName]').last().type(lastName);
  cy.get('input[name=pin]').type(pin);
  findByText('LOG IN').click();

  cy.answerSecurityQuestions();
});

Cypress.Commands.add('submitSecurityAnswers', () => {
  cy.get('[data-testid=label-100]').type('00');
  cy.get('[data-testid=label-101]').type('00');
  cy.get('[data-testid=label-102]').type('00');
  cy.get('button[type=submit]').click();
});

Cypress.Commands.add('answerSecurityQuestions', () => {
  const securityQuestionsPageText = 'To protect your account, please provide answers to the following questions';

  cy.contains(securityQuestionsPageText, { timeout: 7000, log: false, failOnNonExistent: false }).then(($securityText) => {
    if ($securityText.length) {
      cy.submitSecurityAnswers();
    } else {
      cy.contains('Answer security questions', { timeout: 5000 }).then(($answerSecurity) => {
        if ($answerSecurity.length) {
          cy.wrap($answerSecurity).click();
          cy.contains(securityQuestionsPageText, { timeout: 10000 }).should('be.visible');
          cy.submitSecurityAnswers();
        } else {
          cy.contains('See options', { timeout: 5000 }).then(($seeOptions) => {
            if ($seeOptions.length) {
              cy.wrap($seeOptions).click();
              cy.contains('Answer security questions', { timeout: 10000 }).click();

              cy.contains(securityQuestionsPageText, { timeout: 5000 }).should('be.visible');
              cy.submitSecurityAnswers();
            } else {
              cy.log('Warning: None of the expected verification options were found.');
            }
          });
        }
      });
    }
  });
});

Cypress.Commands.add('loginViaSSO', () => {
  cy.loginSteps('1988538367', 'Loytest', '1717');
});

Cypress.Commands.add('loginViaSSOPointsClubPlusMember', () => {
  cy.loginSteps('1989050586', 'Loytest', '1717');
});

Cypress.Commands.add('logoutViaSSO', () => {
  cy.get(testId('logout-button')).first().should('be.visible').click();
});
