import { testId } from '../selectors';

Cypress.Commands.add('loginSteps', (memberId, lastName, pin) => {
  const { findByText } = cy;

  cy.get(testId('qff-login-tab')).first().should('be.visible').click();

  cy.get('input[name=memberId]').type(memberId);
  cy.get('input[name=lastName]').last().type(lastName);
  cy.get('input[name=pin]').type(pin);
  findByText('LOG IN').click();

  cy.answerSecurityQuestions();
});

Cypress.Commands.add('submitSecurityAnswers', () => {
  cy.get('[data-testid=label-100]').type('00');
  cy.get('[data-testid=label-101]').type('00');
  cy.get('[data-testid=label-102]').type('00');
  cy.get('button[type=submit]').click();
});

Cypress.Commands.add('answerSecurityQuestions', () => {
  // Simple, predictable approach: always use "See options" flow
  // This ensures consistent behaviour across different auth states
  cy.contains('See options', { timeout: 15000 }).should('be.visible').click();
  cy.contains('Answer security questions', { timeout: 10000 }).should('be.visible').click();
  cy.contains('To protect your account, please provide answers to the following questions', { timeout: 10000 }).should('be.visible');
  cy.submitSecurityAnswers();
});

Cypress.Commands.add('loginViaSSO', () => {
  cy.loginSteps('**********', 'Loytest', '1717');
});

Cypress.Commands.add('loginViaSSOPointsClubPlusMember', () => {
  cy.loginSteps('**********', 'Loytest', '1717');
});

Cypress.Commands.add('logoutViaSSO', () => {
  cy.get(testId('logout-button')).first().should('be.visible').click();
});
